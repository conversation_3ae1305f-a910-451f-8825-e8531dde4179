'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { AlertTriangle, Search, Clock, FileX, Brain, Smartphone, Shield, Zap } from 'lucide-react'
import { cn } from '@/lib/utils'

const problems = [
  {
    icon: FileX,
    title: "Lost Receipts",
    description: "70% of people lose receipts within 6 months",
    color: "text-red-400"
  },
  {
    icon: Clock,
    title: "Missed Deadlines",
    description: "Warranty claims expire without notice",
    color: "text-orange-400"
  },
  {
    icon: Search,
    title: "Manual Tracking",
    description: "Chaotic spreadsheets and forgotten items",
    color: "text-yellow-400"
  },
  {
    icon: AlertTriangle,
    title: "Lost Value",
    description: "Thousands in unclaimed warranties annually",
    color: "text-red-500"
  }
]

const solutions = [
  {
    icon: Brain,
    title: "AI-Powered Detection",
    description: "Automatically extract product info from any receipt or photo",
    color: "text-neon-blue"
  },
  {
    icon: Smartphone,
    title: "Smart Reminders",
    description: "Never miss warranty expiry or service dates again",
    color: "text-neon-green"
  },
  {
    icon: Shield,
    title: "Secure Storage",
    description: "All your warranties and documents in one safe place",
    color: "text-neon-purple"
  },
  {
    icon: Zap,
    title: "Instant Claims",
    description: "Quick access to warranty info when you need it most",
    color: "text-neon-pink"
  }
]

const ProblemSolutionSection = () => {
  const [activeView, setActiveView] = useState<'problem' | 'solution'>('problem')
  const [currentIndex, setCurrentIndex] = useState(0)

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prev) => {
        const maxIndex = activeView === 'problem' ? problems.length - 1 : solutions.length - 1
        if (prev >= maxIndex) {
          // Switch views when reaching the end
          setActiveView(activeView === 'problem' ? 'solution' : 'problem')
          return 0
        }
        return prev + 1
      })
    }, 3000)

    return () => clearInterval(interval)
  }, [activeView])

  const currentItems = activeView === 'problem' ? problems : solutions

  return (
    <section className="py-24 relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-b from-dark-950 via-dark-900 to-dark-950" />
      
      {/* Animated background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-neon-blue/10 rounded-full blur-xl animate-pulse" />
        <div className="absolute bottom-1/4 right-1/4 w-40 h-40 bg-neon-purple/10 rounded-full blur-xl animate-pulse" />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <motion.h2 
            className="text-4xl lg:text-6xl font-display font-bold mb-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <span className="gradient-text">The Problem</span>
            <span className="text-white"> vs </span>
            <span className="gradient-text-neon">Our Solution</span>
          </motion.h2>
          
          <motion.p 
            className="text-xl text-gray-300 max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            Traditional warranty tracking is broken. We're fixing it with AI and smart automation.
          </motion.p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Morphing Visualization */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="relative"
          >
            <div className="glass-card p-8 h-96 flex flex-col justify-center">
              {/* View Toggle */}
              <div className="flex justify-center mb-8">
                <div className="glass rounded-full p-1 flex">
                  <button
                    onClick={() => setActiveView('problem')}
                    className={cn(
                      "px-4 py-2 rounded-full text-sm font-medium transition-all duration-300",
                      activeView === 'problem' 
                        ? "bg-red-500/20 text-red-400 shadow-lg" 
                        : "text-gray-400 hover:text-white"
                    )}
                  >
                    Problems
                  </button>
                  <button
                    onClick={() => setActiveView('solution')}
                    className={cn(
                      "px-4 py-2 rounded-full text-sm font-medium transition-all duration-300",
                      activeView === 'solution' 
                        ? "bg-neon-blue/20 text-neon-blue shadow-lg" 
                        : "text-gray-400 hover:text-white"
                    )}
                  >
                    Solutions
                  </button>
                </div>
              </div>

              {/* Morphing Content */}
              <AnimatePresence mode="wait">
                <motion.div
                  key={`${activeView}-${currentIndex}`}
                  initial={{ opacity: 0, scale: 0.8, rotateY: 90 }}
                  animate={{ opacity: 1, scale: 1, rotateY: 0 }}
                  exit={{ opacity: 0, scale: 0.8, rotateY: -90 }}
                  transition={{ duration: 0.6, ease: "easeInOut" }}
                  className="text-center"
                >
                  <motion.div
                    className={cn(
                      "w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center",
                      activeView === 'problem' 
                        ? "bg-red-500/20 border-2 border-red-500/30" 
                        : "bg-neon-blue/20 border-2 border-neon-blue/30"
                    )}
                    animate={{ 
                      scale: [1, 1.1, 1],
                      rotate: [0, 5, -5, 0]
                    }}
                    transition={{ 
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  >
                    {React.createElement(currentItems[currentIndex].icon, {
                      className: `w-8 h-8 ${currentItems[currentIndex].color}`
                    })}
                  </motion.div>

                  <h3 className="text-2xl font-bold mb-4 text-white">
                    {currentItems[currentIndex].title}
                  </h3>
                  
                  <p className="text-gray-300 text-lg">
                    {currentItems[currentIndex].description}
                  </p>
                </motion.div>
              </AnimatePresence>

              {/* Progress Indicators */}
              <div className="flex justify-center space-x-2 mt-8">
                {currentItems.map((_, index) => (
                  <div
                    key={index}
                    className={cn(
                      "w-2 h-2 rounded-full transition-all duration-300",
                      index === currentIndex
                        ? activeView === 'problem' 
                          ? "bg-red-400 w-8" 
                          : "bg-neon-blue w-8"
                        : "bg-gray-600"
                    )}
                  />
                ))}
              </div>
            </div>
          </motion.div>

          {/* Content Side */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            <div>
              <h3 className="text-3xl font-bold mb-4">
                {activeView === 'problem' ? (
                  <span className="text-red-400">Why Current Methods Fail</span>
                ) : (
                  <span className="gradient-text-neon">How WarrantyAI Solves It</span>
                )}
              </h3>
              
              <p className="text-gray-300 text-lg leading-relaxed">
                {activeView === 'problem' ? (
                  "Traditional warranty tracking relies on manual processes that are prone to human error. People lose receipts, forget about warranties, and miss crucial deadlines, resulting in thousands of dollars in lost value every year."
                ) : (
                  "WarrantyAI uses advanced AI to automatically detect, extract, and organize warranty information from any source. Smart reminders ensure you never miss important dates, while secure cloud storage keeps everything safe and accessible."
                )}
              </p>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 gap-6">
              {activeView === 'problem' ? (
                <>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-red-400 mb-2">70%</div>
                    <div className="text-sm text-gray-400">Lose receipts within 6 months</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-red-400 mb-2">$2,000</div>
                    <div className="text-sm text-gray-400">Average unclaimed warranty value</div>
                  </div>
                </>
              ) : (
                <>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-neon-green mb-2">99%</div>
                    <div className="text-sm text-gray-400">AI accuracy in data extraction</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-neon-blue mb-2">24/7</div>
                    <div className="text-sm text-gray-400">Automated monitoring</div>
                  </div>
                </>
              )}
            </div>

            {/* Feature List */}
            <div className="space-y-4">
              {activeView === 'solution' && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.5 }}
                  className="space-y-3"
                >
                  {[
                    "Instant receipt scanning and data extraction",
                    "Smart warranty expiration reminders",
                    "Secure cloud storage with encryption",
                    "One-click warranty claim assistance"
                  ].map((feature, index) => (
                    <motion.div
                      key={feature}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.7 + index * 0.1 }}
                      className="flex items-center space-x-3"
                    >
                      <div className="w-2 h-2 bg-neon-green rounded-full" />
                      <span className="text-gray-300">{feature}</span>
                    </motion.div>
                  ))}
                </motion.div>
              )}
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default ProblemSolutionSection
