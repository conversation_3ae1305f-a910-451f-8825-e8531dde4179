@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap');

:root {
  --background: #0a0a0a;
  --foreground: #ededed;
  --primary: #3b82f6;
  --accent: #a855f7;
  --neon-blue: #00f5ff;
  --neon-purple: #bf00ff;
  --neon-green: #39ff14;
  --neon-pink: #ff1493;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', system-ui, sans-serif;
  line-height: 1.6;
  overflow-x: hidden;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.1);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, var(--primary), var(--accent));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, var(--accent), var(--primary));
}

/* Glass Effects */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
}

.glass-dark {
  background: rgba(15, 23, 42, 0.3);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
}

.glass-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Gradient Text */
.gradient-text {
  background: linear-gradient(135deg, var(--primary), var(--accent), var(--neon-blue));
  background-size: 200% 200%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradient 3s ease infinite;
}

.gradient-text-neon {
  background: linear-gradient(135deg, var(--neon-blue), var(--neon-purple), var(--neon-pink));
  background-size: 200% 200%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradient 2s ease infinite;
}

/* Neon Effects */
.neon-glow {
  box-shadow:
    0 0 5px var(--neon-blue),
    0 0 10px var(--neon-blue),
    0 0 15px var(--neon-blue),
    0 0 20px var(--neon-blue);
}

.neon-text {
  color: var(--neon-blue);
  text-shadow:
    0 0 5px var(--neon-blue),
    0 0 10px var(--neon-blue),
    0 0 15px var(--neon-blue);
}

.neon-border {
  border: 2px solid var(--neon-blue);
  box-shadow:
    inset 0 0 10px var(--neon-blue),
    0 0 10px var(--neon-blue);
}

/* Animated Backgrounds */
.animated-gradient {
  background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
  background-size: 400% 400%;
  animation: gradient 15s ease infinite;
}

.mesh-gradient {
  background:
    radial-gradient(at 40% 20%, hsla(228,100%,74%,1) 0px, transparent 50%),
    radial-gradient(at 80% 0%, hsla(189,100%,56%,1) 0px, transparent 50%),
    radial-gradient(at 0% 50%, hsla(355,100%,93%,1) 0px, transparent 50%),
    radial-gradient(at 80% 50%, hsla(340,100%,76%,1) 0px, transparent 50%),
    radial-gradient(at 0% 100%, hsla(22,100%,77%,1) 0px, transparent 50%),
    radial-gradient(at 80% 100%, hsla(242,100%,70%,1) 0px, transparent 50%),
    radial-gradient(at 0% 0%, hsla(343,100%,76%,1) 0px, transparent 50%);
}

/* Button Styles */
.btn-primary {
  @apply px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg transition-all duration-300 transform hover:scale-105 hover:shadow-lg;
}

.btn-neon {
  @apply px-6 py-3 bg-transparent border-2 border-neon-blue text-neon-blue font-medium rounded-lg transition-all duration-300 hover:bg-neon-blue hover:text-dark-900 hover:shadow-lg;
  box-shadow: 0 0 10px rgba(0, 245, 255, 0.3);
}

.btn-neon:hover {
  box-shadow: 0 0 20px rgba(0, 245, 255, 0.6);
}

.btn-glass {
  @apply glass px-6 py-3 text-white font-medium rounded-lg transition-all duration-300 hover:bg-white/20;
}

/* Card Styles */
.card-hover {
  @apply transition-all duration-300 hover:scale-105 hover:shadow-2xl;
}

.card-glow {
  @apply transition-all duration-300;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.card-glow:hover {
  box-shadow: 0 8px 40px rgba(59, 130, 246, 0.3);
}

/* Loading Animations */
.loading-dots::after {
  content: '';
  animation: dots 1.5s steps(5, end) infinite;
}

@keyframes dots {
  0%, 20% { content: ''; }
  40% { content: '.'; }
  60% { content: '..'; }
  80%, 100% { content: '...'; }
}

/* Parallax */
.parallax {
  transform-style: preserve-3d;
}

.parallax-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

/* Utilities */
.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.text-shadow-lg {
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
}

.backdrop-blur-xs {
  backdrop-filter: blur(2px);
}

/* Custom Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 5px rgba(168, 85, 247, 0.4); }
  50% { box-shadow: 0 0 20px rgba(168, 85, 247, 0.8); }
}

@keyframes text-shimmer {
  0% { background-position: -200% center; }
  100% { background-position: 200% center; }
}

/* Responsive Design */
@media (max-width: 768px) {
  .glass, .glass-dark, .glass-card {
    backdrop-filter: blur(8px);
  }

  .neon-glow {
    box-shadow: 0 0 3px var(--neon-blue), 0 0 6px var(--neon-blue);
  }
}
