'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Upload, Brain, Bell, Camera, Scan, Shield, Zap, CheckCircle } from 'lucide-react'
import <PERSON><PERSON> from '@/components/ui/Button'
import { cn } from '@/lib/utils'

const steps = [
  {
    id: 1,
    title: "Upload & Scan",
    subtitle: "Capture warranty info instantly",
    description: "Simply upload a receipt, take a photo, or forward an email. Our AI instantly recognizes and extracts all warranty information.",
    icon: Upload,
    color: "neon-blue",
    features: [
      "Photo recognition technology",
      "Email auto-import",
      "Bulk receipt processing",
      "Multi-format support"
    ],
    demo: {
      type: "upload",
      content: "Receipt_iPhone15Pro.jpg"
    }
  },
  {
    id: 2,
    title: "AI Processing",
    subtitle: "Smart data extraction",
    description: "Advanced AI analyzes your documents to extract product details, warranty periods, serial numbers, and service information.",
    icon: Brain,
    color: "neon-purple",
    features: [
      "99% accuracy rate",
      "Multi-language support",
      "Brand recognition",
      "Automatic categorization"
    ],
    demo: {
      type: "processing",
      content: {
        product: "iPhone 15 Pro",
        brand: "Apple",
        model: "A3102",
        warranty: "1 Year Limited",
        purchase: "2024-01-15",
        serial: "F2LW8J9K2L"
      }
    }
  },
  {
    id: 3,
    title: "Smart Reminders",
    subtitle: "Never miss important dates",
    description: "Get intelligent notifications before warranties expire, service dates approach, or when it's time for maintenance.",
    icon: Bell,
    color: "neon-green",
    features: [
      "Customizable alerts",
      "Multi-channel notifications",
      "Service scheduling",
      "Claim assistance"
    ],
    demo: {
      type: "reminder",
      content: "Warranty expires in 30 days"
    }
  }
]

const ProcessSection = () => {
  const [activeStep, setActiveStep] = useState(1)
  const [isAnimating, setIsAnimating] = useState(false)

  const handleStepClick = (stepId: number) => {
    if (stepId !== activeStep && !isAnimating) {
      setIsAnimating(true)
      setActiveStep(stepId)
      setTimeout(() => setIsAnimating(false), 600)
    }
  }

  const currentStep = steps.find(step => step.id === activeStep) || steps[0]

  return (
    <section className="py-24 relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-b from-dark-950 via-dark-900 to-dark-950" />
      
      {/* Animated background patterns */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute top-0 left-1/4 w-px h-full bg-gradient-to-b from-transparent via-neon-blue to-transparent" />
        <div className="absolute top-0 right-1/4 w-px h-full bg-gradient-to-b from-transparent via-neon-purple to-transparent" />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <motion.h2 
            className="text-4xl lg:text-6xl font-display font-bold mb-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <span className="gradient-text">How It Works</span>
          </motion.h2>
          
          <motion.p 
            className="text-xl text-gray-300 max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            Three simple steps to never miss a warranty again. Our AI does the heavy lifting.
          </motion.p>
        </div>

        {/* Interactive Steps */}
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Steps Navigation */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            {steps.map((step, index) => (
              <motion.div
                key={step.id}
                className={cn(
                  "relative cursor-pointer transition-all duration-300",
                  activeStep === step.id ? "scale-105" : "hover:scale-102"
                )}
                onClick={() => handleStepClick(step.id)}
                whileHover={{ x: 10 }}
              >
                {/* Connection Line */}
                {index < steps.length - 1 && (
                  <div className="absolute left-6 top-16 w-px h-16 bg-gradient-to-b from-gray-600 to-transparent" />
                )}

                <div className={cn(
                  "glass-card p-6 border-l-4 transition-all duration-300",
                  activeStep === step.id
                    ? step.color === 'neon-blue' ? "border-cyan-400 bg-white/10" :
                      step.color === 'neon-purple' ? "border-purple-400 bg-white/10" :
                      "border-green-400 bg-white/10"
                    : "border-gray-600 hover:border-gray-400"
                )}>
                  <div className="flex items-start space-x-4">
                    {/* Step Icon */}
                    <div className={cn(
                      "w-12 h-12 rounded-full flex items-center justify-center transition-all duration-300",
                      activeStep === step.id
                        ? step.color === 'neon-blue' ? "bg-cyan-400/20 border-2 border-cyan-400" :
                          step.color === 'neon-purple' ? "bg-purple-400/20 border-2 border-purple-400" :
                          "bg-green-400/20 border-2 border-green-400"
                        : "bg-gray-700 border-2 border-gray-600"
                    )}>
                      <step.icon className={cn(
                        "w-6 h-6 transition-colors duration-300",
                        activeStep === step.id
                          ? step.color === 'neon-blue' ? "text-cyan-400" :
                            step.color === 'neon-purple' ? "text-purple-400" :
                            "text-green-400"
                          : "text-gray-400"
                      )} />
                    </div>

                    {/* Step Content */}
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <span className={cn(
                          "text-sm font-medium px-2 py-1 rounded-full",
                          activeStep === step.id
                            ? step.color === 'neon-blue' ? "bg-cyan-400/20 text-cyan-400" :
                              step.color === 'neon-purple' ? "bg-purple-400/20 text-purple-400" :
                              "bg-green-400/20 text-green-400"
                            : "bg-gray-700 text-gray-400"
                        )}>
                          Step {step.id}
                        </span>
                      </div>
                      
                      <h3 className={cn(
                        "text-xl font-bold mb-2 transition-colors duration-300",
                        activeStep === step.id ? "text-white" : "text-gray-300"
                      )}>
                        {step.title}
                      </h3>
                      
                      <p className="text-gray-400 text-sm mb-3">
                        {step.subtitle}
                      </p>

                      {activeStep === step.id && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: "auto" }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3 }}
                        >
                          <p className="text-gray-300 mb-4">
                            {step.description}
                          </p>
                          
                          <div className="space-y-2">
                            {step.features.map((feature, idx) => (
                              <motion.div
                                key={feature}
                                initial={{ opacity: 0, x: -10 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: idx * 0.1 }}
                                className="flex items-center space-x-2"
                              >
                                <CheckCircle className="w-4 h-4 text-green-400" />
                                <span className="text-sm text-gray-400">{feature}</span>
                              </motion.div>
                            ))}
                          </div>
                        </motion.div>
                      )}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Interactive Demo */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="relative"
          >
            <div className="glass-card p-8 h-96 flex flex-col justify-center">
              <AnimatePresence mode="wait">
                <motion.div
                  key={activeStep}
                  initial={{ opacity: 0, scale: 0.8, rotateY: 90 }}
                  animate={{ opacity: 1, scale: 1, rotateY: 0 }}
                  exit={{ opacity: 0, scale: 0.8, rotateY: -90 }}
                  transition={{ duration: 0.6, ease: "easeInOut" }}
                  className="text-center"
                >
                  {/* Demo Content */}
                  {currentStep.demo.type === 'upload' && (
                    <div className="space-y-6">
                      <motion.div
                        className="w-20 h-20 mx-auto bg-cyan-400/20 border-2 border-cyan-400 rounded-full flex items-center justify-center"
                        animate={{ scale: [1, 1.1, 1] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      >
                        <Camera className="w-8 h-8 text-cyan-400" />
                      </motion.div>

                      <div className="border-2 border-dashed border-cyan-400/50 rounded-lg p-6">
                        <Upload className="w-8 h-8 mx-auto mb-2 text-cyan-400" />
                        <p className="text-sm text-gray-300">Drop receipt here</p>
                        <p className="text-xs text-gray-500 mt-2">{currentStep.demo.content}</p>
                      </div>
                    </div>
                  )}

                  {currentStep.demo.type === 'processing' && (
                    <div className="space-y-6">
                      <motion.div
                        className="w-20 h-20 mx-auto bg-purple-400/20 border-2 border-purple-400 rounded-full flex items-center justify-center"
                        animate={{ rotate: 360 }}
                        transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
                      >
                        <Scan className="w-8 h-8 text-purple-400" />
                      </motion.div>

                      <div className="bg-gray-800 rounded-lg p-4 space-y-2 text-left">
                        {Object.entries(currentStep.demo.content as any).map(([key, value], idx) => (
                          <motion.div
                            key={key}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: idx * 0.2 }}
                            className="flex justify-between"
                          >
                            <span className="text-xs text-gray-400 capitalize">{key}:</span>
                            <span className="text-xs text-purple-400">{value}</span>
                          </motion.div>
                        ))}
                      </div>
                    </div>
                  )}

                  {currentStep.demo.type === 'reminder' && (
                    <div className="space-y-6">
                      <motion.div
                        className="w-20 h-20 mx-auto bg-green-400/20 border-2 border-green-400 rounded-full flex items-center justify-center"
                        animate={{ scale: [1, 1.2, 1] }}
                        transition={{ duration: 1.5, repeat: Infinity }}
                      >
                        <Bell className="w-8 h-8 text-green-400" />
                      </motion.div>

                      <div className="bg-green-400/10 border border-green-400/30 rounded-lg p-4">
                        <div className="flex items-center space-x-2 mb-2">
                          <Shield className="w-4 h-4 text-green-400" />
                          <span className="text-sm font-medium text-green-400">Warranty Alert</span>
                        </div>
                        <p className="text-sm text-gray-300">{currentStep.demo.content}</p>
                        <Button variant="neon" size="sm" className="mt-3 w-full">
                          View Details
                        </Button>
                      </div>
                    </div>
                  )}
                </motion.div>
              </AnimatePresence>
            </div>
          </motion.div>
        </div>

        {/* CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <Button variant="neon" size="lg" icon={<Zap />}>
            Try It Free Now
          </Button>
        </motion.div>
      </div>
    </section>
  )
}

export default ProcessSection
