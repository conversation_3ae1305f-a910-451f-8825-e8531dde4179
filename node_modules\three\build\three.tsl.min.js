/**
 * @license
 * Copyright 2010-2025 Three.js Authors
 * SPDX-License-Identifier: MIT
 */
import{TSL as e}from"three/webgpu";const t=e.BRDF_GGX,r=e.BRDF_Lambert,a=e.BasicShadowFilter,o=e.Break,i=e.Continue,n=e.DFGApprox,l=e.D_GGX,s=e.Discard,c=e.EPSILON,m=e.F_Schlick,p=e.Fn,d=e.INFINITY,u=e.If,g=e.Switch,h=e.Loop,f=e.NodeShaderStage,x=e.NodeType,b=e.NodeUpdateType,w=e.NodeAccess,v=e.PCFShadowFilter,S=e.PCFSoftShadowFilter,T=e.PI,_=e.PI2,V=e.Return,y=e.Schlick_to_F0,M=e.ScriptableNodeResources,D=e.ShaderNode,F=e.TBNViewMatrix,C=e.VSMShadowFilter,I=e.V_GGX_SmithCorrelated,P=e.abs,R=e.acesFilmicToneMapping,N=e.acos,B=e.add,L=e.addNodeElement,k=e.agxToneMapping,A=e.all,G=e.alphaT,O=e.and,W=e.anisotropy,j=e.anisotropyB,U=e.anisotropyT,z=e.any,q=e.append,E=e.array,Z=e.arrayBuffer,X=e.asin,Y=e.assign,H=e.atan,J=e.atan2,K=e.atomicAdd,Q=e.atomicAnd,$=e.atomicFunc,ee=e.atomicMax,te=e.atomicMin,re=e.atomicOr,ae=e.atomicStore,oe=e.atomicSub,ie=e.atomicXor,ne=e.atomicLoad,le=e.attenuationColor,se=e.attenuationDistance,ce=e.attribute,me=e.attributeArray,pe=e.backgroundBlurriness,de=e.backgroundIntensity,ue=e.backgroundRotation,ge=e.batch,he=e.billboarding,fe=e.bitAnd,xe=e.bitNot,be=e.bitOr,we=e.bitXor,ve=e.bitangentGeometry,Se=e.bitangentLocal,Te=e.bitangentView,_e=e.bitangentWorld,Ve=e.bitcast,ye=e.blendBurn,Me=e.blendColor,De=e.blendDodge,Fe=e.blendOverlay,Ce=e.blendScreen,Ie=e.blur,Pe=e.bool,Re=e.buffer,Ne=e.bufferAttribute,Be=e.bumpMap,Le=e.burn,ke=e.bvec2,Ae=e.bvec3,Ge=e.bvec4,Oe=e.bypass,We=e.cache,je=e.call,Ue=e.cameraFar,ze=e.cameraIndex,qe=e.cameraNear,Ee=e.cameraNormalMatrix,Ze=e.cameraPosition,Xe=e.cameraProjectionMatrix,Ye=e.cameraProjectionMatrixInverse,He=e.cameraViewMatrix,Je=e.cameraWorldMatrix,Ke=e.cbrt,Qe=e.cdl,$e=e.ceil,et=e.checker,tt=e.cineonToneMapping,rt=e.clamp,at=e.clearcoat,ot=e.clearcoatRoughness,it=e.code,nt=e.color,lt=e.colorSpaceToWorking,st=e.colorToDirection,ct=e.compute,mt=e.computeSkinning,pt=e.cond,dt=e.Const,ut=e.context,gt=e.convert,ht=e.convertColorSpace,ft=e.convertToTexture,xt=e.cos,bt=e.cross,wt=e.cubeTexture,vt=e.dFdx,St=e.dFdy,Tt=e.dashSize,_t=e.debug,Vt=e.decrement,yt=e.decrementBefore,Mt=e.defaultBuildStages,Dt=e.defaultShaderStages,Ft=e.defined,Ct=e.degrees,It=e.deltaTime,Pt=e.densityFog,Rt=e.densityFogFactor,Nt=e.depth,Bt=e.depthPass,Lt=e.difference,kt=e.diffuseColor,At=e.directPointLight,Gt=e.directionToColor,Ot=e.dispersion,Wt=e.distance,jt=e.div,Ut=e.dodge,zt=e.dot,qt=e.drawIndex,Et=e.dynamicBufferAttribute,Zt=e.element,Xt=e.emissive,Yt=e.equal,Ht=e.equals,Jt=e.equirectUV,Kt=e.exp,Qt=e.exp2,$t=e.expression,er=e.faceDirection,tr=e.faceForward,rr=e.faceforward,ar=e.float,or=e.floor,ir=e.fog,nr=e.fract,lr=e.frameGroup,sr=e.frameId,cr=e.frontFacing,mr=e.fwidth,pr=e.gain,dr=e.gapSize,ur=e.getConstNodeType,gr=e.getCurrentStack,hr=e.getDirection,fr=e.getDistanceAttenuation,xr=e.getGeometryRoughness,br=e.getNormalFromDepth,wr=e.getParallaxCorrectNormal,vr=e.getRoughness,Sr=e.getScreenPosition,Tr=e.getShIrradianceAt,_r=e.getTextureIndex,Vr=e.getViewPosition,yr=e.getShadowMaterial,Mr=e.getShadowRenderObjectFunction,Dr=e.glsl,Fr=e.glslFn,Cr=e.grayscale,Ir=e.greaterThan,Pr=e.greaterThanEqual,Rr=e.hash,Nr=e.highpModelNormalViewMatrix,Br=e.highpModelViewMatrix,Lr=e.hue,kr=e.increment,Ar=e.incrementBefore,Gr=e.instance,Or=e.instanceIndex,Wr=e.instancedArray,jr=e.instancedBufferAttribute,Ur=e.instancedDynamicBufferAttribute,zr=e.instancedMesh,qr=e.int,Er=e.inverseSqrt,Zr=e.inversesqrt,Xr=e.invocationLocalIndex,Yr=e.invocationSubgroupIndex,Hr=e.ior,Jr=e.iridescence,Kr=e.iridescenceIOR,Qr=e.iridescenceThickness,$r=e.ivec2,ea=e.ivec3,ta=e.ivec4,ra=e.js,aa=e.label,oa=e.length,ia=e.lengthSq,na=e.lessThan,la=e.lessThanEqual,sa=e.lightPosition,ca=e.lightShadowMatrix,ma=e.lightTargetDirection,pa=e.lightTargetPosition,da=e.lightViewPosition,ua=e.lightingContext,ga=e.lights,ha=e.linearDepth,fa=e.linearToneMapping,xa=e.localId,ba=e.globalId,wa=e.log,va=e.log2,Sa=e.logarithmicDepthToViewZ,Ta=e.loop,_a=e.luminance,Va=e.mediumpModelViewMatrix,ya=e.mat2,Ma=e.mat3,Da=e.mat4,Fa=e.matcapUV,Ca=e.materialAO,Ia=e.materialAlphaTest,Pa=e.materialAnisotropy,Ra=e.materialAnisotropyVector,Na=e.materialAttenuationColor,Ba=e.materialAttenuationDistance,La=e.materialClearcoat,ka=e.materialClearcoatNormal,Aa=e.materialClearcoatRoughness,Ga=e.materialColor,Oa=e.materialDispersion,Wa=e.materialEmissive,ja=e.materialIOR,Ua=e.materialIridescence,za=e.materialIridescenceIOR,qa=e.materialIridescenceThickness,Ea=e.materialLightMap,Za=e.materialLineDashOffset,Xa=e.materialLineDashSize,Ya=e.materialLineGapSize,Ha=e.materialLineScale,Ja=e.materialLineWidth,Ka=e.materialMetalness,Qa=e.materialNormal,$a=e.materialOpacity,eo=e.materialPointSize,to=e.materialReference,ro=e.materialReflectivity,ao=e.materialRefractionRatio,oo=e.materialRotation,io=e.materialRoughness,no=e.materialSheen,lo=e.materialSheenRoughness,so=e.materialShininess,co=e.materialSpecular,mo=e.materialSpecularColor,po=e.materialSpecularIntensity,uo=e.materialSpecularStrength,go=e.materialThickness,ho=e.materialTransmission,fo=e.max,xo=e.maxMipLevel,bo=e.metalness,wo=e.min,vo=e.mix,So=e.mixElement,To=e.mod,_o=e.modInt,Vo=e.modelDirection,yo=e.modelNormalMatrix,Mo=e.modelPosition,Do=e.modelRadius,Fo=e.modelScale,Co=e.modelViewMatrix,Io=e.modelViewPosition,Po=e.modelViewProjection,Ro=e.modelWorldMatrix,No=e.modelWorldMatrixInverse,Bo=e.morphReference,Lo=e.mrt,ko=e.mul,Ao=e.mx_aastep,Go=e.mx_cell_noise_float,Oo=e.mx_contrast,Wo=e.mx_fractal_noise_float,jo=e.mx_fractal_noise_vec2,Uo=e.mx_fractal_noise_vec3,zo=e.mx_fractal_noise_vec4,qo=e.mx_hsvtorgb,Eo=e.mx_noise_float,Zo=e.mx_noise_vec3,Xo=e.mx_noise_vec4,Yo=e.mx_ramplr,Ho=e.mx_ramptb,Jo=e.mx_rgbtohsv,Ko=e.mx_safepower,Qo=e.mx_splitlr,$o=e.mx_splittb,ei=e.mx_srgb_texture_to_lin_rec709,ti=e.mx_transform_uv,ri=e.mx_worley_noise_float,ai=e.mx_worley_noise_vec2,oi=e.mx_worley_noise_vec3,ii=e.negate,ni=e.neutralToneMapping,li=e.nodeArray,si=e.nodeImmutable,ci=e.nodeObject,mi=e.nodeObjects,pi=e.nodeProxy,di=e.normalFlat,ui=e.normalGeometry,gi=e.normalLocal,hi=e.normalMap,fi=e.normalView,xi=e.normalWorld,bi=e.normalize,wi=e.not,vi=e.notEqual,Si=e.numWorkgroups,Ti=e.objectDirection,_i=e.objectGroup,Vi=e.objectPosition,yi=e.objectRadius,Mi=e.objectScale,Di=e.objectViewPosition,Fi=e.objectWorldMatrix,Ci=e.oneMinus,Ii=e.or,Pi=e.orthographicDepthToViewZ,Ri=e.oscSawtooth,Ni=e.oscSine,Bi=e.oscSquare,Li=e.oscTriangle,ki=e.output,Ai=e.outputStruct,Gi=e.overlay,Oi=e.overloadingFn,Wi=e.parabola,ji=e.parallaxDirection,Ui=e.parallaxUV,zi=e.parameter,qi=e.pass,Ei=e.passTexture,Zi=e.pcurve,Xi=e.perspectiveDepthToViewZ,Yi=e.pmremTexture,Hi=e.pointUV,Ji=e.pointWidth,Ki=e.positionGeometry,Qi=e.positionLocal,$i=e.positionPrevious,en=e.positionView,tn=e.positionViewDirection,rn=e.positionWorld,an=e.positionWorldDirection,on=e.posterize,nn=e.pow,ln=e.pow2,sn=e.pow3,cn=e.pow4,mn=e.property,pn=e.radians,dn=e.rand,un=e.range,gn=e.rangeFog,hn=e.rangeFogFactor,fn=e.reciprocal,xn=e.lightProjectionUV,bn=e.reference,wn=e.referenceBuffer,vn=e.reflect,Sn=e.reflectVector,Tn=e.reflectView,_n=e.reflector,Vn=e.refract,yn=e.refractVector,Mn=e.refractView,Dn=e.reinhardToneMapping,Fn=e.remainder,Cn=e.remap,In=e.remapClamp,Pn=e.renderGroup,Rn=e.renderOutput,Nn=e.rendererReference,Bn=e.rotate,Ln=e.rotateUV,kn=e.roughness,An=e.round,Gn=e.rtt,On=e.sRGBTransferEOTF,Wn=e.sRGBTransferOETF,jn=e.sampler,Un=e.samplerComparison,zn=e.saturate,qn=e.saturation,En=e.screen,Zn=e.screenCoordinate,Xn=e.screenSize,Yn=e.screenUV,Hn=e.scriptable,Jn=e.scriptableValue,Kn=e.select,Qn=e.setCurrentStack,$n=e.shaderStages,el=e.shadow,tl=e.pointShadow,rl=e.shadowPositionWorld,al=e.sharedUniformGroup,ol=e.shapeCircle,il=e.sheen,nl=e.sheenRoughness,ll=e.shiftLeft,sl=e.shiftRight,cl=e.shininess,ml=e.sign,pl=e.sin,dl=e.sinc,ul=e.skinning,gl=e.smoothstep,hl=e.smoothstepElement,fl=e.specularColor,xl=e.specularF90,bl=e.spherizeUV,wl=e.split,vl=e.spritesheetUV,Sl=e.sqrt,Tl=e.stack,_l=e.step,Vl=e.storage,yl=e.storageBarrier,Ml=e.storageObject,Dl=e.storageTexture,Fl=e.string,Cl=e.struct,Il=e.sub,Pl=e.subgroupIndex,Rl=e.subgroupSize,Nl=e.tan,Bl=e.tangentGeometry,Ll=e.tangentLocal,kl=e.tangentView,Al=e.tangentWorld,Gl=e.temp,Ol=e.texture,Wl=e.texture3D,jl=e.textureBarrier,Ul=e.textureBicubic,zl=e.textureCubeUV,ql=e.textureLoad,El=e.textureSize,Zl=e.textureStore,Xl=e.thickness,Yl=e.threshold,Hl=e.time,Jl=e.timerDelta,Kl=e.timerGlobal,Ql=e.timerLocal,$l=e.toOutputColorSpace,es=e.toWorkingColorSpace,ts=e.toneMapping,rs=e.toneMappingExposure,as=e.toonOutlinePass,os=e.transformDirection,is=e.transformNormal,ns=e.transformNormalToView,ls=e.transformedBentNormalView,ss=e.transformedBitangentView,cs=e.transformedBitangentWorld,ms=e.transformedClearcoatNormalView,ps=e.transformedNormalView,ds=e.transformedNormalWorld,us=e.transformedTangentView,gs=e.transformedTangentWorld,hs=e.transmission,fs=e.transpose,xs=e.tri,bs=e.tri3,ws=e.triNoise3D,vs=e.triplanarTexture,Ss=e.triplanarTextures,Ts=e.trunc,_s=e.tslFn,Vs=e.uint,ys=e.uniform,Ms=e.uniformArray,Ds=e.uniformGroup,Fs=e.uniforms,Cs=e.userData,Is=e.uv,Ps=e.uvec2,Rs=e.uvec3,Ns=e.uvec4,Bs=e.Var,Ls=e.varying,ks=e.varyingProperty,As=e.vec2,Gs=e.vec3,Os=e.vec4,Ws=e.vectorComponents,js=e.velocity,Us=e.vertexColor,zs=e.vertexIndex,qs=e.vibrance,Es=e.viewZToLogarithmicDepth,Zs=e.viewZToOrthographicDepth,Xs=e.viewZToPerspectiveDepth,Ys=e.viewport,Hs=e.viewportBottomLeft,Js=e.viewportCoordinate,Ks=e.viewportDepthTexture,Qs=e.viewportLinearDepth,$s=e.viewportMipTexture,ec=e.viewportResolution,tc=e.viewportSafeUV,rc=e.viewportSharedTexture,ac=e.viewportSize,oc=e.viewportTexture,ic=e.viewportTopLeft,nc=e.viewportUV,lc=e.wgsl,sc=e.wgslFn,cc=e.workgroupArray,mc=e.workgroupBarrier,pc=e.workgroupId,dc=e.workingToColorSpace,uc=e.xor;export{t as BRDF_GGX,r as BRDF_Lambert,a as BasicShadowFilter,o as Break,dt as Const,i as Continue,n as DFGApprox,l as D_GGX,s as Discard,c as EPSILON,m as F_Schlick,p as Fn,d as INFINITY,u as If,h as Loop,w as NodeAccess,f as NodeShaderStage,x as NodeType,b as NodeUpdateType,v as PCFShadowFilter,S as PCFSoftShadowFilter,T as PI,_ as PI2,V as Return,y as Schlick_to_F0,M as ScriptableNodeResources,D as ShaderNode,g as Switch,F as TBNViewMatrix,C as VSMShadowFilter,I as V_GGX_SmithCorrelated,Bs as Var,P as abs,R as acesFilmicToneMapping,N as acos,B as add,L as addNodeElement,k as agxToneMapping,A as all,G as alphaT,O as and,W as anisotropy,j as anisotropyB,U as anisotropyT,z as any,q as append,E as array,Z as arrayBuffer,X as asin,Y as assign,H as atan,J as atan2,K as atomicAdd,Q as atomicAnd,$ as atomicFunc,ne as atomicLoad,ee as atomicMax,te as atomicMin,re as atomicOr,ae as atomicStore,oe as atomicSub,ie as atomicXor,le as attenuationColor,se as attenuationDistance,ce as attribute,me as attributeArray,pe as backgroundBlurriness,de as backgroundIntensity,ue as backgroundRotation,ge as batch,he as billboarding,fe as bitAnd,xe as bitNot,be as bitOr,we as bitXor,ve as bitangentGeometry,Se as bitangentLocal,Te as bitangentView,_e as bitangentWorld,Ve as bitcast,ye as blendBurn,Me as blendColor,De as blendDodge,Fe as blendOverlay,Ce as blendScreen,Ie as blur,Pe as bool,Re as buffer,Ne as bufferAttribute,Be as bumpMap,Le as burn,ke as bvec2,Ae as bvec3,Ge as bvec4,Oe as bypass,We as cache,je as call,Ue as cameraFar,ze as cameraIndex,qe as cameraNear,Ee as cameraNormalMatrix,Ze as cameraPosition,Xe as cameraProjectionMatrix,Ye as cameraProjectionMatrixInverse,He as cameraViewMatrix,Je as cameraWorldMatrix,Ke as cbrt,Qe as cdl,$e as ceil,et as checker,tt as cineonToneMapping,rt as clamp,at as clearcoat,ot as clearcoatRoughness,it as code,nt as color,lt as colorSpaceToWorking,st as colorToDirection,ct as compute,mt as computeSkinning,pt as cond,ut as context,gt as convert,ht as convertColorSpace,ft as convertToTexture,xt as cos,bt as cross,wt as cubeTexture,vt as dFdx,St as dFdy,Tt as dashSize,_t as debug,Vt as decrement,yt as decrementBefore,Mt as defaultBuildStages,Dt as defaultShaderStages,Ft as defined,Ct as degrees,It as deltaTime,Pt as densityFog,Rt as densityFogFactor,Nt as depth,Bt as depthPass,Lt as difference,kt as diffuseColor,At as directPointLight,Gt as directionToColor,Ot as dispersion,Wt as distance,jt as div,Ut as dodge,zt as dot,qt as drawIndex,Et as dynamicBufferAttribute,Zt as element,Xt as emissive,Yt as equal,Ht as equals,Jt as equirectUV,Kt as exp,Qt as exp2,$t as expression,er as faceDirection,tr as faceForward,rr as faceforward,ar as float,or as floor,ir as fog,nr as fract,lr as frameGroup,sr as frameId,cr as frontFacing,mr as fwidth,pr as gain,dr as gapSize,ur as getConstNodeType,gr as getCurrentStack,hr as getDirection,fr as getDistanceAttenuation,xr as getGeometryRoughness,br as getNormalFromDepth,wr as getParallaxCorrectNormal,vr as getRoughness,Sr as getScreenPosition,Tr as getShIrradianceAt,yr as getShadowMaterial,Mr as getShadowRenderObjectFunction,_r as getTextureIndex,Vr as getViewPosition,ba as globalId,Dr as glsl,Fr as glslFn,Cr as grayscale,Ir as greaterThan,Pr as greaterThanEqual,Rr as hash,Nr as highpModelNormalViewMatrix,Br as highpModelViewMatrix,Lr as hue,kr as increment,Ar as incrementBefore,Gr as instance,Or as instanceIndex,Wr as instancedArray,jr as instancedBufferAttribute,Ur as instancedDynamicBufferAttribute,zr as instancedMesh,qr as int,Er as inverseSqrt,Zr as inversesqrt,Xr as invocationLocalIndex,Yr as invocationSubgroupIndex,Hr as ior,Jr as iridescence,Kr as iridescenceIOR,Qr as iridescenceThickness,$r as ivec2,ea as ivec3,ta as ivec4,ra as js,aa as label,oa as length,ia as lengthSq,na as lessThan,la as lessThanEqual,sa as lightPosition,xn as lightProjectionUV,ca as lightShadowMatrix,ma as lightTargetDirection,pa as lightTargetPosition,da as lightViewPosition,ua as lightingContext,ga as lights,ha as linearDepth,fa as linearToneMapping,xa as localId,wa as log,va as log2,Sa as logarithmicDepthToViewZ,Ta as loop,_a as luminance,ya as mat2,Ma as mat3,Da as mat4,Fa as matcapUV,Ca as materialAO,Ia as materialAlphaTest,Pa as materialAnisotropy,Ra as materialAnisotropyVector,Na as materialAttenuationColor,Ba as materialAttenuationDistance,La as materialClearcoat,ka as materialClearcoatNormal,Aa as materialClearcoatRoughness,Ga as materialColor,Oa as materialDispersion,Wa as materialEmissive,ja as materialIOR,Ua as materialIridescence,za as materialIridescenceIOR,qa as materialIridescenceThickness,Ea as materialLightMap,Za as materialLineDashOffset,Xa as materialLineDashSize,Ya as materialLineGapSize,Ha as materialLineScale,Ja as materialLineWidth,Ka as materialMetalness,Qa as materialNormal,$a as materialOpacity,eo as materialPointSize,to as materialReference,ro as materialReflectivity,ao as materialRefractionRatio,oo as materialRotation,io as materialRoughness,no as materialSheen,lo as materialSheenRoughness,so as materialShininess,co as materialSpecular,mo as materialSpecularColor,po as materialSpecularIntensity,uo as materialSpecularStrength,go as materialThickness,ho as materialTransmission,fo as max,xo as maxMipLevel,Va as mediumpModelViewMatrix,bo as metalness,wo as min,vo as mix,So as mixElement,To as mod,_o as modInt,Vo as modelDirection,yo as modelNormalMatrix,Mo as modelPosition,Do as modelRadius,Fo as modelScale,Co as modelViewMatrix,Io as modelViewPosition,Po as modelViewProjection,Ro as modelWorldMatrix,No as modelWorldMatrixInverse,Bo as morphReference,Lo as mrt,ko as mul,Ao as mx_aastep,Go as mx_cell_noise_float,Oo as mx_contrast,Wo as mx_fractal_noise_float,jo as mx_fractal_noise_vec2,Uo as mx_fractal_noise_vec3,zo as mx_fractal_noise_vec4,qo as mx_hsvtorgb,Eo as mx_noise_float,Zo as mx_noise_vec3,Xo as mx_noise_vec4,Yo as mx_ramplr,Ho as mx_ramptb,Jo as mx_rgbtohsv,Ko as mx_safepower,Qo as mx_splitlr,$o as mx_splittb,ei as mx_srgb_texture_to_lin_rec709,ti as mx_transform_uv,ri as mx_worley_noise_float,ai as mx_worley_noise_vec2,oi as mx_worley_noise_vec3,ii as negate,ni as neutralToneMapping,li as nodeArray,si as nodeImmutable,ci as nodeObject,mi as nodeObjects,pi as nodeProxy,di as normalFlat,ui as normalGeometry,gi as normalLocal,hi as normalMap,fi as normalView,xi as normalWorld,bi as normalize,wi as not,vi as notEqual,Si as numWorkgroups,Ti as objectDirection,_i as objectGroup,Vi as objectPosition,yi as objectRadius,Mi as objectScale,Di as objectViewPosition,Fi as objectWorldMatrix,Ci as oneMinus,Ii as or,Pi as orthographicDepthToViewZ,Ri as oscSawtooth,Ni as oscSine,Bi as oscSquare,Li as oscTriangle,ki as output,Ai as outputStruct,Gi as overlay,Oi as overloadingFn,Wi as parabola,ji as parallaxDirection,Ui as parallaxUV,zi as parameter,qi as pass,Ei as passTexture,Zi as pcurve,Xi as perspectiveDepthToViewZ,Yi as pmremTexture,tl as pointShadow,Hi as pointUV,Ji as pointWidth,Ki as positionGeometry,Qi as positionLocal,$i as positionPrevious,en as positionView,tn as positionViewDirection,rn as positionWorld,an as positionWorldDirection,on as posterize,nn as pow,ln as pow2,sn as pow3,cn as pow4,mn as property,pn as radians,dn as rand,un as range,gn as rangeFog,hn as rangeFogFactor,fn as reciprocal,bn as reference,wn as referenceBuffer,vn as reflect,Sn as reflectVector,Tn as reflectView,_n as reflector,Vn as refract,yn as refractVector,Mn as refractView,Dn as reinhardToneMapping,Fn as remainder,Cn as remap,In as remapClamp,Pn as renderGroup,Rn as renderOutput,Nn as rendererReference,Bn as rotate,Ln as rotateUV,kn as roughness,An as round,Gn as rtt,On as sRGBTransferEOTF,Wn as sRGBTransferOETF,jn as sampler,Un as samplerComparison,zn as saturate,qn as saturation,En as screen,Zn as screenCoordinate,Xn as screenSize,Yn as screenUV,Hn as scriptable,Jn as scriptableValue,Kn as select,Qn as setCurrentStack,$n as shaderStages,el as shadow,rl as shadowPositionWorld,ol as shapeCircle,al as sharedUniformGroup,il as sheen,nl as sheenRoughness,ll as shiftLeft,sl as shiftRight,cl as shininess,ml as sign,pl as sin,dl as sinc,ul as skinning,gl as smoothstep,hl as smoothstepElement,fl as specularColor,xl as specularF90,bl as spherizeUV,wl as split,vl as spritesheetUV,Sl as sqrt,Tl as stack,_l as step,Vl as storage,yl as storageBarrier,Ml as storageObject,Dl as storageTexture,Fl as string,Cl as struct,Il as sub,Pl as subgroupIndex,Rl as subgroupSize,Nl as tan,Bl as tangentGeometry,Ll as tangentLocal,kl as tangentView,Al as tangentWorld,Gl as temp,Ol as texture,Wl as texture3D,jl as textureBarrier,Ul as textureBicubic,zl as textureCubeUV,ql as textureLoad,El as textureSize,Zl as textureStore,Xl as thickness,Yl as threshold,Hl as time,Jl as timerDelta,Kl as timerGlobal,Ql as timerLocal,$l as toOutputColorSpace,es as toWorkingColorSpace,ts as toneMapping,rs as toneMappingExposure,as as toonOutlinePass,os as transformDirection,is as transformNormal,ns as transformNormalToView,ls as transformedBentNormalView,ss as transformedBitangentView,cs as transformedBitangentWorld,ms as transformedClearcoatNormalView,ps as transformedNormalView,ds as transformedNormalWorld,us as transformedTangentView,gs as transformedTangentWorld,hs as transmission,fs as transpose,xs as tri,bs as tri3,ws as triNoise3D,vs as triplanarTexture,Ss as triplanarTextures,Ts as trunc,_s as tslFn,Vs as uint,ys as uniform,Ms as uniformArray,Ds as uniformGroup,Fs as uniforms,Cs as userData,Is as uv,Ps as uvec2,Rs as uvec3,Ns as uvec4,Ls as varying,ks as varyingProperty,As as vec2,Gs as vec3,Os as vec4,Ws as vectorComponents,js as velocity,Us as vertexColor,zs as vertexIndex,qs as vibrance,Es as viewZToLogarithmicDepth,Zs as viewZToOrthographicDepth,Xs as viewZToPerspectiveDepth,Ys as viewport,Hs as viewportBottomLeft,Js as viewportCoordinate,Ks as viewportDepthTexture,Qs as viewportLinearDepth,$s as viewportMipTexture,ec as viewportResolution,tc as viewportSafeUV,rc as viewportSharedTexture,ac as viewportSize,oc as viewportTexture,ic as viewportTopLeft,nc as viewportUV,lc as wgsl,sc as wgslFn,cc as workgroupArray,mc as workgroupBarrier,pc as workgroupId,dc as workingToColorSpace,uc as xor};
