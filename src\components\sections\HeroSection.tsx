'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Upload, Scan, Bell, Shield, Zap, CheckCircle } from 'lucide-react'
import Button from '@/components/ui/Button'
import { cn } from '@/lib/utils'

// Demo data for the warranty tracking simulation
const demoItems = [
  {
    id: 1,
    name: 'iPhone 15 Pro',
    brand: 'Apple',
    purchaseDate: '2024-01-15',
    warrantyExpiry: '2025-01-15',
    status: 'active',
    daysLeft: 45,
    category: 'Electronics'
  },
  {
    id: 2,
    name: 'Samsung Refrigerator',
    brand: 'Samsung',
    purchaseDate: '2023-06-20',
    warrantyExpiry: '2025-06-20',
    status: 'active',
    daysLeft: 180,
    category: 'Appliances'
  },
  {
    id: 3,
    name: 'MacBook Pro M3',
    brand: 'Apple',
    purchaseDate: '2024-03-10',
    warrantyExpiry: '2025-03-10',
    status: 'expiring',
    daysLeft: 15,
    category: 'Electronics'
  }
]

const HeroSection = () => {
  const [currentStep, setCurrentStep] = useState(0)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [extractedData, setExtractedData] = useState<any>(null)
  const [showItems, setShowItems] = useState(false)

  // Demo simulation steps
  const steps = [
    'Upload Receipt',
    'AI Extraction',
    'Add to Dashboard',
    'Set Reminders'
  ]

  // Auto-play demo simulation
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentStep((prev) => {
        if (prev === 0) {
          // Start upload simulation
          setUploadProgress(0)
          const progressInterval = setInterval(() => {
            setUploadProgress((p) => {
              if (p >= 100) {
                clearInterval(progressInterval)
                return 100
              }
              return p + 10
            })
          }, 100)
          return 1
        } else if (prev === 1) {
          // AI extraction simulation
          setTimeout(() => {
            setExtractedData({
              product: 'Sony WH-1000XM5',
              brand: 'Sony',
              model: 'WH-1000XM5',
              purchaseDate: '2024-12-01',
              warrantyPeriod: '2 years',
              serialNumber: 'SN123456789'
            })
          }, 1500)
          return 2
        } else if (prev === 2) {
          // Add to dashboard
          setTimeout(() => {
            setShowItems(true)
          }, 1000)
          return 3
        } else {
          // Reset demo
          setUploadProgress(0)
          setExtractedData(null)
          setShowItems(false)
          return 0
        }
      })
    }, 4000)

    return () => clearInterval(interval)
  }, [])

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Multi-layer Background */}
      <div className="absolute inset-0 z-0">
        {/* Layer 1: Base gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-dark-900 via-dark-800 to-dark-950" />
        
        {/* Layer 2: Animated mesh gradient */}
        <div className="absolute inset-0 mesh-gradient opacity-30" />
        
        {/* Layer 3: Floating particles */}
        <div className="absolute inset-0">
          {[...Array(50)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-neon-blue rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -20, 0],
                opacity: [0.3, 1, 0.3],
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>
        
        {/* Layer 4: Geometric patterns */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 border border-neon-purple rounded-full animate-spin-slow" />
          <div className="absolute bottom-1/4 right-1/4 w-48 h-48 border border-neon-green rounded-lg animate-pulse" />
        </div>
        
        {/* Layer 5: Glow effects */}
        <div className="absolute top-1/3 left-1/2 w-96 h-96 bg-neon-blue/20 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-1/3 right-1/2 w-80 h-80 bg-neon-purple/20 rounded-full blur-3xl animate-pulse" />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Column - Hero Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center lg:text-left"
          >
            <motion.h1 
              className="text-5xl lg:text-7xl font-display font-bold mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <span className="gradient-text-neon">Never Miss</span>
              <br />
              <span className="text-white">a Warranty</span>
              <br />
              <span className="gradient-text">Again</span>
            </motion.h1>

            <motion.p 
              className="text-xl text-gray-300 mb-8 max-w-2xl"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              Smart AI assistant to track, manage, and remind you of all warranties, 
              services, and coverage across electronics, home, vehicles, and more.
            </motion.p>

            <motion.div 
              className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              <Button variant="neon" size="lg" icon={<Zap />}>
                Start Free Trial
              </Button>
              <Button variant="glass" size="lg" icon={<Scan />}>
                Watch Demo
              </Button>
            </motion.div>

            {/* Trust indicators */}
            <motion.div 
              className="flex items-center justify-center lg:justify-start space-x-6 mt-8"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.8 }}
            >
              <div className="flex items-center space-x-2">
                <Shield className="w-5 h-5 text-neon-green" />
                <span className="text-sm text-gray-400">Secure & Private</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5 text-neon-blue" />
                <span className="text-sm text-gray-400">AI Powered</span>
              </div>
            </motion.div>
          </motion.div>

          {/* Right Column - Working Demo */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="relative"
          >
            <div className="glass-card p-8 max-w-md mx-auto">
              <h3 className="text-xl font-semibold mb-6 text-center gradient-text">
                Live Demo - Warranty Tracker
              </h3>

              {/* Demo Steps Indicator */}
              <div className="flex justify-between mb-6">
                {steps.map((step, index) => (
                  <div key={step} className="flex flex-col items-center">
                    <div className={cn(
                      "w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium transition-all duration-300",
                      index <= currentStep 
                        ? "bg-neon-blue text-dark-900 neon-glow" 
                        : "bg-gray-700 text-gray-400"
                    )}>
                      {index + 1}
                    </div>
                    <span className="text-xs mt-1 text-gray-400">{step}</span>
                  </div>
                ))}
              </div>

              {/* Demo Content */}
              <div className="space-y-4">
                <AnimatePresence mode="wait">
                  {currentStep === 0 && (
                    <motion.div
                      key="upload"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      className="border-2 border-dashed border-gray-600 rounded-lg p-6 text-center"
                    >
                      <Upload className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                      <p className="text-sm text-gray-400">Drop receipt or take photo</p>
                    </motion.div>
                  )}

                  {currentStep === 1 && (
                    <motion.div
                      key="processing"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      className="space-y-3"
                    >
                      <div className="bg-gray-800 rounded-lg p-4">
                        <div className="flex items-center space-x-2 mb-2">
                          <Scan className="w-4 h-4 text-neon-blue animate-pulse" />
                          <span className="text-sm">AI Extracting Data...</span>
                        </div>
                        <div className="w-full bg-gray-700 rounded-full h-2">
                          <div 
                            className="bg-neon-blue h-2 rounded-full transition-all duration-300"
                            style={{ width: `${uploadProgress}%` }}
                          />
                        </div>
                      </div>
                      
                      {extractedData && (
                        <motion.div
                          initial={{ opacity: 0, scale: 0.9 }}
                          animate={{ opacity: 1, scale: 1 }}
                          className="bg-gray-800 rounded-lg p-4 space-y-2"
                        >
                          <div className="flex justify-between">
                            <span className="text-xs text-gray-400">Product:</span>
                            <span className="text-xs text-neon-green">{extractedData.product}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-xs text-gray-400">Brand:</span>
                            <span className="text-xs">{extractedData.brand}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-xs text-gray-400">Warranty:</span>
                            <span className="text-xs text-neon-blue">{extractedData.warrantyPeriod}</span>
                          </div>
                        </motion.div>
                      )}
                    </motion.div>
                  )}

                  {(currentStep === 2 || currentStep === 3) && showItems && (
                    <motion.div
                      key="dashboard"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      className="space-y-3"
                    >
                      {demoItems.slice(0, 2).map((item, index) => (
                        <motion.div
                          key={item.id}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.2 }}
                          className="bg-gray-800 rounded-lg p-3 flex items-center justify-between"
                        >
                          <div>
                            <p className="text-sm font-medium">{item.name}</p>
                            <p className="text-xs text-gray-400">{item.brand}</p>
                          </div>
                          <div className="text-right">
                            <p className={cn(
                              "text-xs font-medium",
                              item.status === 'expiring' ? "text-neon-pink" : "text-neon-green"
                            )}>
                              {item.daysLeft} days
                            </p>
                            {currentStep === 3 && (
                              <motion.div
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                className="flex items-center space-x-1 mt-1"
                              >
                                <Bell className="w-3 h-3 text-neon-blue" />
                                <span className="text-xs text-neon-blue">Reminder Set</span>
                              </motion.div>
                            )}
                          </div>
                        </motion.div>
                      ))}
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default HeroSection
