"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/ProcessSection.tsx":
/*!****************************************************!*\
  !*** ./src/components/sections/ProcessSection.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Bell_Brain_Camera_CheckCircle_Scan_Shield_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Brain,Camera,CheckCircle,Scan,Shield,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Brain_Camera_CheckCircle_Scan_Shield_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Brain,Camera,CheckCircle,Scan,Shield,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Brain_Camera_CheckCircle_Scan_Shield_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Brain,Camera,CheckCircle,Scan,Shield,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Brain_Camera_CheckCircle_Scan_Shield_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Brain,Camera,CheckCircle,Scan,Shield,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Brain_Camera_CheckCircle_Scan_Shield_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Brain,Camera,CheckCircle,Scan,Shield,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Brain_Camera_CheckCircle_Scan_Shield_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Brain,Camera,CheckCircle,Scan,Shield,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scan.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Brain_Camera_CheckCircle_Scan_Shield_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Brain,Camera,CheckCircle,Scan,Shield,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Brain_Camera_CheckCircle_Scan_Shield_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Brain,Camera,CheckCircle,Scan,Shield,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst steps = [\n    {\n        id: 1,\n        title: \"Upload & Scan\",\n        subtitle: \"Capture warranty info instantly\",\n        description: \"Simply upload a receipt, take a photo, or forward an email. Our AI instantly recognizes and extracts all warranty information.\",\n        icon: _barrel_optimize_names_Bell_Brain_Camera_CheckCircle_Scan_Shield_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        color: \"neon-blue\",\n        features: [\n            \"Photo recognition technology\",\n            \"Email auto-import\",\n            \"Bulk receipt processing\",\n            \"Multi-format support\"\n        ],\n        demo: {\n            type: \"upload\",\n            content: \"Receipt_iPhone15Pro.jpg\"\n        }\n    },\n    {\n        id: 2,\n        title: \"AI Processing\",\n        subtitle: \"Smart data extraction\",\n        description: \"Advanced AI analyzes your documents to extract product details, warranty periods, serial numbers, and service information.\",\n        icon: _barrel_optimize_names_Bell_Brain_Camera_CheckCircle_Scan_Shield_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        color: \"neon-purple\",\n        features: [\n            \"99% accuracy rate\",\n            \"Multi-language support\",\n            \"Brand recognition\",\n            \"Automatic categorization\"\n        ],\n        demo: {\n            type: \"processing\",\n            content: {\n                product: \"iPhone 15 Pro\",\n                brand: \"Apple\",\n                model: \"A3102\",\n                warranty: \"1 Year Limited\",\n                purchase: \"2024-01-15\",\n                serial: \"F2LW8J9K2L\"\n            }\n        }\n    },\n    {\n        id: 3,\n        title: \"Smart Reminders\",\n        subtitle: \"Never miss important dates\",\n        description: \"Get intelligent notifications before warranties expire, service dates approach, or when it's time for maintenance.\",\n        icon: _barrel_optimize_names_Bell_Brain_Camera_CheckCircle_Scan_Shield_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        color: \"neon-green\",\n        features: [\n            \"Customizable alerts\",\n            \"Multi-channel notifications\",\n            \"Service scheduling\",\n            \"Claim assistance\"\n        ],\n        demo: {\n            type: \"reminder\",\n            content: \"Warranty expires in 30 days\"\n        }\n    }\n];\nconst ProcessSection = ()=>{\n    _s();\n    const [activeStep, setActiveStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [isAnimating, setIsAnimating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleStepClick = (stepId)=>{\n        if (stepId !== activeStep && !isAnimating) {\n            setIsAnimating(true);\n            setActiveStep(stepId);\n            setTimeout(()=>setIsAnimating(false), 600);\n        }\n    };\n    const currentStep = steps.find((step)=>step.id === activeStep) || steps[0];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-24 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-b from-dark-950 via-dark-900 to-dark-950\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-30\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 left-1/4 w-px h-full bg-gradient-to-b from-transparent via-neon-blue to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 right-1/4 w-px h-full bg-gradient-to-b from-transparent via-neon-purple to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.h2, {\n                                className: \"text-4xl lg:text-6xl font-display font-bold mb-6\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"gradient-text\",\n                                    children: \"How It Works\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.p, {\n                                className: \"text-xl text-gray-300 max-w-3xl mx-auto\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.2\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: \"Three simple steps to never miss a warranty again. Our AI does the heavy lifting.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid lg:grid-cols-2 gap-12 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"space-y-6\",\n                                children: steps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"relative cursor-pointer transition-all duration-300\", activeStep === step.id ? \"scale-105\" : \"hover:scale-102\"),\n                                        onClick: ()=>handleStepClick(step.id),\n                                        whileHover: {\n                                            x: 10\n                                        },\n                                        children: [\n                                            index < steps.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute left-6 top-16 w-px h-16 bg-gradient-to-b from-gray-600 to-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"glass-card p-6 border-l-4 transition-all duration-300\", activeStep === step.id ? step.color === 'neon-blue' ? \"border-cyan-400 bg-white/10\" : step.color === 'neon-purple' ? \"border-purple-400 bg-white/10\" : \"border-green-400 bg-white/10\" : \"border-gray-600 hover:border-gray-400\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"w-12 h-12 rounded-full flex items-center justify-center transition-all duration-300\", activeStep === step.id ? step.color === 'neon-blue' ? \"bg-cyan-400/20 border-2 border-cyan-400\" : step.color === 'neon-purple' ? \"bg-purple-400/20 border-2 border-purple-400\" : \"bg-green-400/20 border-2 border-green-400\" : \"bg-gray-700 border-2 border-gray-600\"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(step.icon, {\n                                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"w-6 h-6 transition-colors duration-300\", activeStep === step.id ? step.color === 'neon-blue' ? \"text-cyan-400\" : step.color === 'neon-purple' ? \"text-purple-400\" : \"text-green-400\" : \"text-gray-400\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                lineNumber: 165,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                            lineNumber: 157,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm font-medium px-2 py-1 rounded-full\", activeStep === step.id ? \"bg-\".concat(step.color, \"/20 text-\").concat(step.color) : \"bg-gray-700 text-gray-400\"),\n                                                                        children: [\n                                                                            \"Step \",\n                                                                            step.id\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                        lineNumber: 178,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                    lineNumber: 177,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-xl font-bold mb-2 transition-colors duration-300\", activeStep === step.id ? \"text-white\" : \"text-gray-300\"),\n                                                                    children: step.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                    lineNumber: 188,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-400 text-sm mb-3\",\n                                                                    children: step.subtitle\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                    lineNumber: 195,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                activeStep === step.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                                    initial: {\n                                                                        opacity: 0,\n                                                                        height: 0\n                                                                    },\n                                                                    animate: {\n                                                                        opacity: 1,\n                                                                        height: \"auto\"\n                                                                    },\n                                                                    exit: {\n                                                                        opacity: 0,\n                                                                        height: 0\n                                                                    },\n                                                                    transition: {\n                                                                        duration: 0.3\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-300 mb-4\",\n                                                                            children: step.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                            lineNumber: 206,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: step.features.map((feature, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                                                    initial: {\n                                                                                        opacity: 0,\n                                                                                        x: -10\n                                                                                    },\n                                                                                    animate: {\n                                                                                        opacity: 1,\n                                                                                        x: 0\n                                                                                    },\n                                                                                    transition: {\n                                                                                        delay: idx * 0.1\n                                                                                    },\n                                                                                    className: \"flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Brain_Camera_CheckCircle_Scan_Shield_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                                            className: \"w-4 h-4 text-neon-green\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                                            lineNumber: 219,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-sm text-gray-400\",\n                                                                                            children: feature\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                                            lineNumber: 220,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, feature, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                                    lineNumber: 212,\n                                                                                    columnNumber: 31\n                                                                                }, undefined))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                            lineNumber: 210,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                    lineNumber: 200,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, step.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: 50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.2\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"relative\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"glass-card p-8 h-96 flex flex-col justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.AnimatePresence, {\n                                        mode: \"wait\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                scale: 0.8,\n                                                rotateY: 90\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                scale: 1,\n                                                rotateY: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                scale: 0.8,\n                                                rotateY: -90\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                ease: \"easeInOut\"\n                                            },\n                                            className: \"text-center\",\n                                            children: [\n                                                currentStep.demo.type === 'upload' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                            className: \"w-20 h-20 mx-auto bg-neon-blue/20 border-2 border-neon-blue rounded-full flex items-center justify-center\",\n                                                            animate: {\n                                                                scale: [\n                                                                    1,\n                                                                    1.1,\n                                                                    1\n                                                                ]\n                                                            },\n                                                            transition: {\n                                                                duration: 2,\n                                                                repeat: Infinity\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Brain_Camera_CheckCircle_Scan_Shield_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"w-8 h-8 text-neon-blue\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border-2 border-dashed border-neon-blue/50 rounded-lg p-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Brain_Camera_CheckCircle_Scan_Shield_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    className: \"w-8 h-8 mx-auto mb-2 text-neon-blue\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                    lineNumber: 263,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-300\",\n                                                                    children: \"Drop receipt here\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                    lineNumber: 264,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500 mt-2\",\n                                                                    children: currentStep.demo.content\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                    lineNumber: 265,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                currentStep.demo.type === 'processing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                            className: \"w-20 h-20 mx-auto bg-neon-purple/20 border-2 border-neon-purple rounded-full flex items-center justify-center\",\n                                                            animate: {\n                                                                rotate: 360\n                                                            },\n                                                            transition: {\n                                                                duration: 3,\n                                                                repeat: Infinity,\n                                                                ease: \"linear\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Brain_Camera_CheckCircle_Scan_Shield_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"w-8 h-8 text-neon-purple\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                lineNumber: 277,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-gray-800 rounded-lg p-4 space-y-2 text-left\",\n                                                            children: Object.entries(currentStep.demo.content).map((param, idx)=>{\n                                                                let [key, value] = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                                    initial: {\n                                                                        opacity: 0,\n                                                                        x: -20\n                                                                    },\n                                                                    animate: {\n                                                                        opacity: 1,\n                                                                        x: 0\n                                                                    },\n                                                                    transition: {\n                                                                        delay: idx * 0.2\n                                                                    },\n                                                                    className: \"flex justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-400 capitalize\",\n                                                                            children: [\n                                                                                key,\n                                                                                \":\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                            lineNumber: 289,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-neon-purple\",\n                                                                            children: value\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                            lineNumber: 290,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, key, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                    lineNumber: 282,\n                                                                    columnNumber: 27\n                                                                }, undefined);\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                currentStep.demo.type === 'reminder' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                            className: \"w-20 h-20 mx-auto bg-neon-green/20 border-2 border-neon-green rounded-full flex items-center justify-center\",\n                                                            animate: {\n                                                                scale: [\n                                                                    1,\n                                                                    1.2,\n                                                                    1\n                                                                ]\n                                                            },\n                                                            transition: {\n                                                                duration: 1.5,\n                                                                repeat: Infinity\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Brain_Camera_CheckCircle_Scan_Shield_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"w-8 h-8 text-neon-green\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-neon-green/10 border border-neon-green/30 rounded-lg p-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Brain_Camera_CheckCircle_Scan_Shield_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"w-4 h-4 text-neon-green\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                            lineNumber: 309,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm font-medium text-neon-green\",\n                                                                            children: \"Warranty Alert\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                            lineNumber: 310,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                    lineNumber: 308,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-300\",\n                                                                    children: currentStep.demo.content\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                    lineNumber: 312,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                    variant: \"neon\",\n                                                                    size: \"sm\",\n                                                                    className: \"mt-3 w-full\",\n                                                                    children: \"View Details\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                    lineNumber: 313,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, activeStep, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.4\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        className: \"text-center mt-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            variant: \"neon\",\n                            size: \"lg\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Brain_Camera_CheckCircle_Scan_Shield_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 50\n                            }, void 0),\n                            children: \"Try It Free Now\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                            lineNumber: 333,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProcessSection, \"b5N3qZPgQVv4k89ys75gz0XaIJc=\");\n_c = ProcessSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProcessSection);\nvar _c;\n$RefreshReg$(_c, \"ProcessSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/ProcessSection.tsx\n"));

/***/ })

});