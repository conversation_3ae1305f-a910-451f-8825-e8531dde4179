"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/ProcessSection.tsx":
/*!****************************************************!*\
  !*** ./src/components/sections/ProcessSection.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Bell_Brain_Camera_CheckCircle_Scan_Shield_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Brain,Camera,CheckCircle,Scan,Shield,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Brain_Camera_CheckCircle_Scan_Shield_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Brain,Camera,CheckCircle,Scan,Shield,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Brain_Camera_CheckCircle_Scan_Shield_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Brain,Camera,CheckCircle,Scan,Shield,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Brain_Camera_CheckCircle_Scan_Shield_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Brain,Camera,CheckCircle,Scan,Shield,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Brain_Camera_CheckCircle_Scan_Shield_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Brain,Camera,CheckCircle,Scan,Shield,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Brain_Camera_CheckCircle_Scan_Shield_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Brain,Camera,CheckCircle,Scan,Shield,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scan.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Brain_Camera_CheckCircle_Scan_Shield_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Brain,Camera,CheckCircle,Scan,Shield,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Brain_Camera_CheckCircle_Scan_Shield_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Brain,Camera,CheckCircle,Scan,Shield,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst steps = [\n    {\n        id: 1,\n        title: \"Upload & Scan\",\n        subtitle: \"Capture warranty info instantly\",\n        description: \"Simply upload a receipt, take a photo, or forward an email. Our AI instantly recognizes and extracts all warranty information.\",\n        icon: _barrel_optimize_names_Bell_Brain_Camera_CheckCircle_Scan_Shield_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        color: \"neon-blue\",\n        features: [\n            \"Photo recognition technology\",\n            \"Email auto-import\",\n            \"Bulk receipt processing\",\n            \"Multi-format support\"\n        ],\n        demo: {\n            type: \"upload\",\n            content: \"Receipt_iPhone15Pro.jpg\"\n        }\n    },\n    {\n        id: 2,\n        title: \"AI Processing\",\n        subtitle: \"Smart data extraction\",\n        description: \"Advanced AI analyzes your documents to extract product details, warranty periods, serial numbers, and service information.\",\n        icon: _barrel_optimize_names_Bell_Brain_Camera_CheckCircle_Scan_Shield_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        color: \"neon-purple\",\n        features: [\n            \"99% accuracy rate\",\n            \"Multi-language support\",\n            \"Brand recognition\",\n            \"Automatic categorization\"\n        ],\n        demo: {\n            type: \"processing\",\n            content: {\n                product: \"iPhone 15 Pro\",\n                brand: \"Apple\",\n                model: \"A3102\",\n                warranty: \"1 Year Limited\",\n                purchase: \"2024-01-15\",\n                serial: \"F2LW8J9K2L\"\n            }\n        }\n    },\n    {\n        id: 3,\n        title: \"Smart Reminders\",\n        subtitle: \"Never miss important dates\",\n        description: \"Get intelligent notifications before warranties expire, service dates approach, or when it's time for maintenance.\",\n        icon: _barrel_optimize_names_Bell_Brain_Camera_CheckCircle_Scan_Shield_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        color: \"neon-green\",\n        features: [\n            \"Customizable alerts\",\n            \"Multi-channel notifications\",\n            \"Service scheduling\",\n            \"Claim assistance\"\n        ],\n        demo: {\n            type: \"reminder\",\n            content: \"Warranty expires in 30 days\"\n        }\n    }\n];\nconst ProcessSection = ()=>{\n    _s();\n    const [activeStep, setActiveStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [isAnimating, setIsAnimating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleStepClick = (stepId)=>{\n        if (stepId !== activeStep && !isAnimating) {\n            setIsAnimating(true);\n            setActiveStep(stepId);\n            setTimeout(()=>setIsAnimating(false), 600);\n        }\n    };\n    const currentStep = steps.find((step)=>step.id === activeStep) || steps[0];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-24 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-b from-dark-950 via-dark-900 to-dark-950\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-30\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 left-1/4 w-px h-full bg-gradient-to-b from-transparent via-neon-blue to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 right-1/4 w-px h-full bg-gradient-to-b from-transparent via-neon-purple to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.h2, {\n                                className: \"text-4xl lg:text-6xl font-display font-bold mb-6\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"gradient-text\",\n                                    children: \"How It Works\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.p, {\n                                className: \"text-xl text-gray-300 max-w-3xl mx-auto\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.2\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: \"Three simple steps to never miss a warranty again. Our AI does the heavy lifting.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid lg:grid-cols-2 gap-12 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"space-y-6\",\n                                children: steps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"relative cursor-pointer transition-all duration-300\", activeStep === step.id ? \"scale-105\" : \"hover:scale-102\"),\n                                        onClick: ()=>handleStepClick(step.id),\n                                        whileHover: {\n                                            x: 10\n                                        },\n                                        children: [\n                                            index < steps.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute left-6 top-16 w-px h-16 bg-gradient-to-b from-gray-600 to-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"glass-card p-6 border-l-4 transition-all duration-300\", activeStep === step.id ? step.color === 'neon-blue' ? \"border-cyan-400 bg-white/10\" : step.color === 'neon-purple' ? \"border-purple-400 bg-white/10\" : \"border-green-400 bg-white/10\" : \"border-gray-600 hover:border-gray-400\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"w-12 h-12 rounded-full flex items-center justify-center transition-all duration-300\", activeStep === step.id ? step.color === 'neon-blue' ? \"bg-cyan-400/20 border-2 border-cyan-400\" : step.color === 'neon-purple' ? \"bg-purple-400/20 border-2 border-purple-400\" : \"bg-green-400/20 border-2 border-green-400\" : \"bg-gray-700 border-2 border-gray-600\"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(step.icon, {\n                                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"w-6 h-6 transition-colors duration-300\", activeStep === step.id ? step.color === 'neon-blue' ? \"text-cyan-400\" : step.color === 'neon-purple' ? \"text-purple-400\" : \"text-green-400\" : \"text-gray-400\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                lineNumber: 165,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                            lineNumber: 157,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm font-medium px-2 py-1 rounded-full\", activeStep === step.id ? step.color === 'neon-blue' ? \"bg-cyan-400/20 text-cyan-400\" : step.color === 'neon-purple' ? \"bg-purple-400/20 text-purple-400\" : \"bg-green-400/20 text-green-400\" : \"bg-gray-700 text-gray-400\"),\n                                                                        children: [\n                                                                            \"Step \",\n                                                                            step.id\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                        lineNumber: 178,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                    lineNumber: 177,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-xl font-bold mb-2 transition-colors duration-300\", activeStep === step.id ? \"text-white\" : \"text-gray-300\"),\n                                                                    children: step.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                    lineNumber: 190,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-400 text-sm mb-3\",\n                                                                    children: step.subtitle\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                    lineNumber: 197,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                activeStep === step.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                                    initial: {\n                                                                        opacity: 0,\n                                                                        height: 0\n                                                                    },\n                                                                    animate: {\n                                                                        opacity: 1,\n                                                                        height: \"auto\"\n                                                                    },\n                                                                    exit: {\n                                                                        opacity: 0,\n                                                                        height: 0\n                                                                    },\n                                                                    transition: {\n                                                                        duration: 0.3\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-300 mb-4\",\n                                                                            children: step.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                            lineNumber: 208,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-2\",\n                                                                            children: step.features.map((feature, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                                                    initial: {\n                                                                                        opacity: 0,\n                                                                                        x: -10\n                                                                                    },\n                                                                                    animate: {\n                                                                                        opacity: 1,\n                                                                                        x: 0\n                                                                                    },\n                                                                                    transition: {\n                                                                                        delay: idx * 0.1\n                                                                                    },\n                                                                                    className: \"flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Brain_Camera_CheckCircle_Scan_Shield_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                                            className: \"w-4 h-4 text-green-400\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                                            lineNumber: 221,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-sm text-gray-400\",\n                                                                                            children: feature\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                                            lineNumber: 222,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, feature, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                                    lineNumber: 214,\n                                                                                    columnNumber: 31\n                                                                                }, undefined))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                            lineNumber: 212,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                    lineNumber: 202,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, step.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: 50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.2\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"relative\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"glass-card p-8 h-96 flex flex-col justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.AnimatePresence, {\n                                        mode: \"wait\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                scale: 0.8,\n                                                rotateY: 90\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                scale: 1,\n                                                rotateY: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                scale: 0.8,\n                                                rotateY: -90\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                ease: \"easeInOut\"\n                                            },\n                                            className: \"text-center\",\n                                            children: [\n                                                currentStep.demo.type === 'upload' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                            className: \"w-20 h-20 mx-auto bg-cyan-400/20 border-2 border-cyan-400 rounded-full flex items-center justify-center\",\n                                                            animate: {\n                                                                scale: [\n                                                                    1,\n                                                                    1.1,\n                                                                    1\n                                                                ]\n                                                            },\n                                                            transition: {\n                                                                duration: 2,\n                                                                repeat: Infinity\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Brain_Camera_CheckCircle_Scan_Shield_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"w-8 h-8 text-cyan-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                lineNumber: 261,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                            lineNumber: 256,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border-2 border-dashed border-cyan-400/50 rounded-lg p-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Brain_Camera_CheckCircle_Scan_Shield_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    className: \"w-8 h-8 mx-auto mb-2 text-cyan-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                    lineNumber: 265,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-300\",\n                                                                    children: \"Drop receipt here\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                    lineNumber: 266,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500 mt-2\",\n                                                                    children: currentStep.demo.content\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                    lineNumber: 267,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                currentStep.demo.type === 'processing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                            className: \"w-20 h-20 mx-auto bg-purple-400/20 border-2 border-purple-400 rounded-full flex items-center justify-center\",\n                                                            animate: {\n                                                                rotate: 360\n                                                            },\n                                                            transition: {\n                                                                duration: 3,\n                                                                repeat: Infinity,\n                                                                ease: \"linear\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Brain_Camera_CheckCircle_Scan_Shield_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"w-8 h-8 text-purple-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                lineNumber: 279,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                            lineNumber: 274,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-gray-800 rounded-lg p-4 space-y-2 text-left\",\n                                                            children: Object.entries(currentStep.demo.content).map((param, idx)=>{\n                                                                let [key, value] = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                                    initial: {\n                                                                        opacity: 0,\n                                                                        x: -20\n                                                                    },\n                                                                    animate: {\n                                                                        opacity: 1,\n                                                                        x: 0\n                                                                    },\n                                                                    transition: {\n                                                                        delay: idx * 0.2\n                                                                    },\n                                                                    className: \"flex justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-400 capitalize\",\n                                                                            children: [\n                                                                                key,\n                                                                                \":\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                            lineNumber: 291,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-purple-400\",\n                                                                            children: value\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                            lineNumber: 292,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, key, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                    lineNumber: 284,\n                                                                    columnNumber: 27\n                                                                }, undefined);\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                currentStep.demo.type === 'reminder' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                            className: \"w-20 h-20 mx-auto bg-green-400/20 border-2 border-green-400 rounded-full flex items-center justify-center\",\n                                                            animate: {\n                                                                scale: [\n                                                                    1,\n                                                                    1.2,\n                                                                    1\n                                                                ]\n                                                            },\n                                                            transition: {\n                                                                duration: 1.5,\n                                                                repeat: Infinity\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Brain_Camera_CheckCircle_Scan_Shield_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"w-8 h-8 text-green-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                lineNumber: 306,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-green-400/10 border border-green-400/30 rounded-lg p-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Brain_Camera_CheckCircle_Scan_Shield_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"w-4 h-4 text-green-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                            lineNumber: 311,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm font-medium text-green-400\",\n                                                                            children: \"Warranty Alert\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                            lineNumber: 312,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                    lineNumber: 310,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-300\",\n                                                                    children: currentStep.demo.content\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                    lineNumber: 314,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                    variant: \"neon\",\n                                                                    size: \"sm\",\n                                                                    className: \"mt-3 w-full\",\n                                                                    children: \"View Details\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                                    lineNumber: 315,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, activeStep, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.4\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        className: \"text-center mt-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            variant: \"neon\",\n                            size: \"lg\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Brain_Camera_CheckCircle_Scan_Shield_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 50\n                            }, void 0),\n                            children: \"Try It Free Now\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\ProcessSection.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProcessSection, \"b5N3qZPgQVv4k89ys75gz0XaIJc=\");\n_c = ProcessSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProcessSection);\nvar _c;\n$RefreshReg$(_c, \"ProcessSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/ProcessSection.tsx\n"));

/***/ })

});