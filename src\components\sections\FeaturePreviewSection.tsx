'use client'

import React, { useEffect, useRef, useState } from 'react'
import { motion, useScroll, useTransform, AnimatePresence } from 'framer-motion'
import { 
  Smartphone, 
  Brain, 
  Shield, 
  Bell, 
  Camera, 
  Cloud, 
  Zap, 
  Eye,
  Calendar,
  FileText,
  Search,
  Settings
} from 'lucide-react'
import { cn } from '@/lib/utils'

const features = [
  {
    id: 1,
    title: "AI-Powered Recognition",
    description: "Advanced computer vision instantly extracts warranty information from any receipt, photo, or document.",
    icon: Brain,
    color: "cyan-400",
    stats: "99% accuracy",
    demo: "receipt-scan"
  },
  {
    id: 2,
    title: "Smart Notifications",
    description: "Intelligent reminders ensure you never miss warranty expiry dates or important service windows.",
    icon: Bell,
    color: "purple-400",
    stats: "24/7 monitoring",
    demo: "notifications"
  },
  {
    id: 3,
    title: "Secure Cloud Storage",
    description: "Military-grade encryption keeps all your warranty documents safe and accessible from anywhere.",
    icon: Shield,
    color: "green-400",
    stats: "Bank-level security",
    demo: "cloud-storage"
  },
  {
    id: 4,
    title: "Universal Compatibility",
    description: "Works with any device, any receipt format, and integrates with your existing email and shopping accounts.",
    icon: Smartphone,
    color: "blue-400",
    stats: "All platforms",
    demo: "compatibility"
  }
]

const FeaturePreviewSection = () => {
  const containerRef = useRef<HTMLDivElement>(null)
  const [activeFeature, setActiveFeature] = useState(0)
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  })

  // Parallax transforms for different layers
  const backgroundY = useTransform(scrollYProgress, [0, 1], ["0%", "50%"])
  const layer1Y = useTransform(scrollYProgress, [0, 1], ["0%", "25%"])
  const layer2Y = useTransform(scrollYProgress, [0, 1], ["0%", "15%"])
  const layer3Y = useTransform(scrollYProgress, [0, 1], ["0%", "10%"])

  // Auto-cycle through features
  useEffect(() => {
    const interval = setInterval(() => {
      setActiveFeature((prev) => (prev + 1) % features.length)
    }, 4000)
    return () => clearInterval(interval)
  }, [])

  const currentFeature = features[activeFeature]

  return (
    <section ref={containerRef} className="relative py-24 overflow-hidden">
      {/* Multi-layer Parallax Background */}
      <div className="absolute inset-0">
        {/* Layer 1: Base gradient */}
        <motion.div 
          style={{ y: backgroundY }}
          className="absolute inset-0 bg-gradient-to-br from-dark-950 via-dark-900 to-dark-950"
        />
        
        {/* Layer 2: Animated mesh */}
        <motion.div 
          style={{ y: layer1Y }}
          className="absolute inset-0 opacity-20"
        >
          <div className="mesh-gradient h-full w-full" />
        </motion.div>
        
        {/* Layer 3: Geometric patterns */}
        <motion.div 
          style={{ y: layer2Y }}
          className="absolute inset-0 opacity-30"
        >
          <div className="absolute top-1/4 left-1/4 w-64 h-64 border border-cyan-400/20 rounded-full animate-spin-slow" />
          <div className="absolute bottom-1/4 right-1/4 w-48 h-48 border border-purple-400/20 rounded-lg animate-pulse" />
          <div className="absolute top-3/4 left-3/4 w-32 h-32 border border-green-400/20 rounded-full animate-ping-slow" />
        </motion.div>
        
        {/* Layer 4: Floating particles */}
        <motion.div 
          style={{ y: layer3Y }}
          className="absolute inset-0"
        >
          {[...Array(30)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-white/30 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -30, 0],
                opacity: [0.3, 1, 0.3],
                scale: [1, 1.5, 1],
              }}
              transition={{
                duration: 4 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </motion.div>
        
        {/* Layer 5: Glow effects */}
        <motion.div 
          style={{ y: layer1Y }}
          className="absolute inset-0"
        >
          <div className="absolute top-1/3 left-1/2 w-96 h-96 bg-cyan-400/10 rounded-full blur-3xl animate-pulse" />
          <div className="absolute bottom-1/3 right-1/2 w-80 h-80 bg-purple-400/10 rounded-full blur-3xl animate-pulse" />
        </motion.div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <motion.h2 
            className="text-4xl lg:text-6xl font-display font-bold mb-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <span className="gradient-text">Powerful Features</span>
          </motion.h2>
          
          <motion.p 
            className="text-xl text-gray-300 max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            Experience the future of warranty management with cutting-edge AI and intuitive design.
          </motion.p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Feature List */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            {features.map((feature, index) => (
              <motion.div
                key={feature.id}
                className={cn(
                  "relative cursor-pointer transition-all duration-500",
                  activeFeature === index ? "scale-105" : "hover:scale-102"
                )}
                onClick={() => setActiveFeature(index)}
                whileHover={{ x: 10 }}
              >
                <div className={cn(
                  "glass-card p-6 border-l-4 transition-all duration-500",
                  activeFeature === index 
                    ? `border-${feature.color} bg-white/10 shadow-2xl` 
                    : "border-gray-600 hover:border-gray-400"
                )}>
                  <div className="flex items-start space-x-4">
                    {/* Feature Icon */}
                    <div className={cn(
                      "w-12 h-12 rounded-full flex items-center justify-center transition-all duration-500",
                      activeFeature === index 
                        ? `bg-${feature.color}/20 border-2 border-${feature.color} shadow-lg` 
                        : "bg-gray-700 border-2 border-gray-600"
                    )}>
                      <feature.icon className={cn(
                        "w-6 h-6 transition-colors duration-500",
                        activeFeature === index ? `text-${feature.color}` : "text-gray-400"
                      )} />
                    </div>

                    {/* Feature Content */}
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className={cn(
                          "text-xl font-bold transition-colors duration-500",
                          activeFeature === index ? "text-white" : "text-gray-300"
                        )}>
                          {feature.title}
                        </h3>
                        
                        <span className={cn(
                          "text-xs font-medium px-2 py-1 rounded-full transition-all duration-500",
                          activeFeature === index 
                            ? `bg-${feature.color}/20 text-${feature.color}` 
                            : "bg-gray-700 text-gray-400"
                        )}>
                          {feature.stats}
                        </span>
                      </div>
                      
                      <p className={cn(
                        "text-gray-400 transition-colors duration-500",
                        activeFeature === index && "text-gray-300"
                      )}>
                        {feature.description}
                      </p>

                      {/* Progress bar */}
                      {activeFeature === index && (
                        <motion.div
                          initial={{ width: 0 }}
                          animate={{ width: "100%" }}
                          transition={{ duration: 4 }}
                          className={`h-1 bg-${feature.color} rounded-full mt-3`}
                        />
                      )}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Interactive Demo */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="relative"
          >
            <div className="glass-card p-8 h-96 flex flex-col justify-center">
              <AnimatePresence mode="wait">
                <motion.div
                  key={activeFeature}
                  initial={{ opacity: 0, scale: 0.8, rotateY: 90 }}
                  animate={{ opacity: 1, scale: 1, rotateY: 0 }}
                  exit={{ opacity: 0, scale: 0.8, rotateY: -90 }}
                  transition={{ duration: 0.6, ease: "easeInOut" }}
                  className="text-center"
                >
                  {/* Feature Demo */}
                  <motion.div
                    className={`w-20 h-20 mx-auto bg-${currentFeature.color}/20 border-2 border-${currentFeature.color} rounded-full flex items-center justify-center mb-6`}
                    animate={{ 
                      scale: [1, 1.1, 1],
                      rotate: currentFeature.demo === 'receipt-scan' ? [0, 360] : [0, 5, -5, 0]
                    }}
                    transition={{ 
                      duration: currentFeature.demo === 'receipt-scan' ? 3 : 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  >
                    <currentFeature.icon className={`w-8 h-8 text-${currentFeature.color}`} />
                  </motion.div>

                  <h3 className="text-2xl font-bold mb-4 text-white">
                    {currentFeature.title}
                  </h3>
                  
                  <p className="text-gray-300 mb-6">
                    {currentFeature.description}
                  </p>

                  {/* Demo-specific content */}
                  <div className="space-y-3">
                    {currentFeature.demo === 'receipt-scan' && (
                      <div className="bg-gray-800 rounded-lg p-4">
                        <div className="flex items-center space-x-2 mb-2">
                          <Camera className="w-4 h-4 text-cyan-400" />
                          <span className="text-sm text-cyan-400">Scanning Receipt...</span>
                        </div>
                        <div className="w-full bg-gray-700 rounded-full h-2">
                          <motion.div 
                            className="bg-cyan-400 h-2 rounded-full"
                            initial={{ width: 0 }}
                            animate={{ width: "100%" }}
                            transition={{ duration: 2, repeat: Infinity }}
                          />
                        </div>
                      </div>
                    )}

                    {currentFeature.demo === 'notifications' && (
                      <div className="space-y-2">
                        {['Warranty expires in 30 days', 'Service reminder due', 'New warranty added'].map((notif, idx) => (
                          <motion.div
                            key={notif}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: idx * 0.5 }}
                            className="bg-purple-400/10 border border-purple-400/30 rounded-lg p-2 text-left"
                          >
                            <div className="flex items-center space-x-2">
                              <Bell className="w-3 h-3 text-purple-400" />
                              <span className="text-xs text-gray-300">{notif}</span>
                            </div>
                          </motion.div>
                        ))}
                      </div>
                    )}

                    {currentFeature.demo === 'cloud-storage' && (
                      <div className="bg-gray-800 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm text-gray-400">Storage Used</span>
                          <span className="text-sm text-green-400">2.4GB / 100GB</span>
                        </div>
                        <div className="w-full bg-gray-700 rounded-full h-2">
                          <div className="bg-green-400 h-2 rounded-full w-1/12" />
                        </div>
                        <div className="flex items-center space-x-2 mt-2">
                          <Cloud className="w-4 h-4 text-green-400" />
                          <span className="text-xs text-green-400">Synced & Secure</span>
                        </div>
                      </div>
                    )}

                    {currentFeature.demo === 'compatibility' && (
                      <div className="grid grid-cols-3 gap-2">
                        {[Smartphone, FileText, Search].map((Icon, idx) => (
                          <motion.div
                            key={idx}
                            initial={{ opacity: 0, scale: 0 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ delay: idx * 0.2 }}
                            className="bg-blue-400/10 border border-blue-400/30 rounded-lg p-3 flex items-center justify-center"
                          >
                            <Icon className="w-6 h-6 text-blue-400" />
                          </motion.div>
                        ))}
                      </div>
                    )}
                  </div>
                </motion.div>
              </AnimatePresence>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default FeaturePreviewSection
