'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Check, Zap, Crown, Rocket, Star, Shield, Infinity } from 'lucide-react'
import Button from '@/components/ui/Button'
import { cn } from '@/lib/utils'

const plans = [
  {
    id: 'free',
    name: 'Free',
    price: 0,
    period: 'forever',
    description: 'Perfect for getting started with basic warranty tracking',
    icon: Shield,
    color: 'gray',
    popular: false,
    features: [
      'Up to 10 warranty items',
      'Manual receipt upload',
      'Basic reminders',
      'Mobile app access',
      'Email support'
    ],
    limitations: [
      'No AI extraction',
      'No auto-import',
      'Limited storage'
    ]
  },
  {
    id: 'pro',
    name: 'Pro',
    price: 9.99,
    period: 'month',
    description: 'Advanced features for power users and families',
    icon: Zap,
    color: 'cyan',
    popular: true,
    features: [
      'Unlimited warranty items',
      'AI-powered extraction',
      'Auto-import from email',
      'Smart reminders',
      'Cloud storage (10GB)',
      'Priority support',
      'Advanced analytics',
      'Family sharing (5 members)'
    ],
    limitations: []
  },
  {
    id: 'business',
    name: 'Business',
    price: 29.99,
    period: 'month',
    description: 'Complete solution for businesses and teams',
    icon: Crown,
    color: 'purple',
    popular: false,
    features: [
      'Everything in Pro',
      'Team management',
      'Bulk import tools',
      'API access',
      'Custom integrations',
      'Unlimited storage',
      'Dedicated support',
      'Advanced reporting',
      'White-label options'
    ],
    limitations: []
  }
]

const PricingSection = () => {
  const [hoveredPlan, setHoveredPlan] = useState<string | null>(null)
  const [billingPeriod, setBillingPeriod] = useState<'monthly' | 'yearly'>('monthly')

  const getPrice = (plan: typeof plans[0]) => {
    if (plan.price === 0) return 0
    return billingPeriod === 'yearly' ? plan.price * 10 : plan.price // 2 months free on yearly
  }

  return (
    <section className="py-24 relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-b from-dark-950 via-dark-900 to-dark-950" />
      
      {/* Animated background elements */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-cyan-400/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-purple-400/10 rounded-full blur-3xl animate-pulse" />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <motion.h2 
            className="text-4xl lg:text-6xl font-display font-bold mb-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <span className="gradient-text">Simple Pricing</span>
          </motion.h2>
          
          <motion.p 
            className="text-xl text-gray-300 max-w-3xl mx-auto mb-8"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            Choose the perfect plan for your warranty tracking needs. Start free, upgrade anytime.
          </motion.p>

          {/* Billing Toggle */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="flex items-center justify-center space-x-4"
          >
            <span className={cn(
              "text-sm font-medium transition-colors duration-300",
              billingPeriod === 'monthly' ? "text-white" : "text-gray-400"
            )}>
              Monthly
            </span>
            
            <button
              onClick={() => setBillingPeriod(billingPeriod === 'monthly' ? 'yearly' : 'monthly')}
              className="relative w-14 h-7 bg-gray-700 rounded-full transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-cyan-400"
            >
              <motion.div
                className="absolute top-1 w-5 h-5 bg-white rounded-full shadow-lg"
                animate={{
                  x: billingPeriod === 'yearly' ? 32 : 4
                }}
                transition={{ type: "spring", stiffness: 500, damping: 30 }}
              />
            </button>
            
            <div className="flex items-center space-x-2">
              <span className={cn(
                "text-sm font-medium transition-colors duration-300",
                billingPeriod === 'yearly' ? "text-white" : "text-gray-400"
              )}>
                Yearly
              </span>
              <span className="bg-green-400/20 text-green-400 text-xs px-2 py-1 rounded-full">
                Save 20%
              </span>
            </div>
          </motion.div>
        </div>

        {/* Pricing Cards */}
        <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          {plans.map((plan, index) => (
            <motion.div
              key={plan.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }}
              className="relative"
              onMouseEnter={() => setHoveredPlan(plan.id)}
              onMouseLeave={() => setHoveredPlan(null)}
            >
              {/* Popular Badge */}
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-20">
                  <div className="bg-gradient-to-r from-cyan-400 to-purple-400 text-dark-900 px-4 py-1 rounded-full text-sm font-medium flex items-center space-x-1">
                    <Star className="w-4 h-4" />
                    <span>Most Popular</span>
                  </div>
                </div>
              )}

              <motion.div
                className={cn(
                  "glass-card p-8 h-full relative overflow-hidden transition-all duration-500",
                  plan.popular && "border-2 border-cyan-400/50",
                  hoveredPlan === plan.id && "scale-105 shadow-2xl"
                )}
                animate={{
                  borderRadius: hoveredPlan === plan.id ? "24px" : "20px"
                }}
                transition={{ duration: 0.3 }}
              >
                {/* Hover Glow Effect */}
                <AnimatePresence>
                  {hoveredPlan === plan.id && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      className={`absolute inset-0 bg-${plan.color === 'cyan' ? 'cyan' : plan.color === 'purple' ? 'purple' : 'gray'}-400/5 rounded-2xl`}
                    />
                  )}
                </AnimatePresence>

                <div className="relative z-10">
                  {/* Plan Header */}
                  <div className="text-center mb-8">
                    <div className={cn(
                      "w-16 h-16 mx-auto rounded-full flex items-center justify-center mb-4 transition-all duration-300",
                      plan.color === 'cyan' ? "bg-cyan-400/20 border-2 border-cyan-400" :
                      plan.color === 'purple' ? "bg-purple-400/20 border-2 border-purple-400" :
                      "bg-gray-400/20 border-2 border-gray-400"
                    )}>
                      <plan.icon className={cn(
                        "w-8 h-8",
                        plan.color === 'cyan' ? "text-cyan-400" :
                        plan.color === 'purple' ? "text-purple-400" :
                        "text-gray-400"
                      )} />
                    </div>
                    
                    <h3 className="text-2xl font-bold text-white mb-2">{plan.name}</h3>
                    <p className="text-gray-400 text-sm">{plan.description}</p>
                  </div>

                  {/* Price */}
                  <div className="text-center mb-8">
                    <div className="flex items-baseline justify-center space-x-1">
                      <span className="text-4xl font-bold text-white">
                        ${getPrice(plan)}
                      </span>
                      {plan.price > 0 && (
                        <span className="text-gray-400">
                          /{billingPeriod === 'yearly' ? 'year' : plan.period}
                        </span>
                      )}
                    </div>
                    
                    {billingPeriod === 'yearly' && plan.price > 0 && (
                      <div className="text-sm text-green-400 mt-1">
                        Save ${(plan.price * 2).toFixed(2)} per year
                      </div>
                    )}
                  </div>

                  {/* Features */}
                  <div className="space-y-4 mb-8">
                    {plan.features.map((feature, idx) => (
                      <motion.div
                        key={feature}
                        initial={{ opacity: 0, x: -10 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ delay: idx * 0.1 }}
                        viewport={{ once: true }}
                        className="flex items-center space-x-3"
                      >
                        <Check className={cn(
                          "w-5 h-5 flex-shrink-0",
                          plan.color === 'cyan' ? "text-cyan-400" :
                          plan.color === 'purple' ? "text-purple-400" :
                          "text-gray-400"
                        )} />
                        <span className="text-gray-300 text-sm">{feature}</span>
                      </motion.div>
                    ))}
                  </div>

                  {/* CTA Button */}
                  <Button
                    variant={plan.popular ? "neon" : "glass"}
                    size="lg"
                    className="w-full"
                    icon={plan.id === 'free' ? <Shield /> : plan.id === 'pro' ? <Zap /> : <Rocket />}
                  >
                    {plan.id === 'free' ? 'Start Free' : 'Get Started'}
                  </Button>

                  {/* Limitations */}
                  {plan.limitations.length > 0 && (
                    <div className="mt-6 pt-6 border-t border-gray-700">
                      <p className="text-xs text-gray-500 mb-2">Limitations:</p>
                      {plan.limitations.map((limitation, idx) => (
                        <div key={limitation} className="flex items-center space-x-2 text-xs text-gray-500">
                          <div className="w-1 h-1 bg-gray-500 rounded-full" />
                          <span>{limitation}</span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </motion.div>
            </motion.div>
          ))}
        </div>

        {/* Trust Indicators */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="flex items-center justify-center space-x-8 text-gray-400">
            <div className="flex items-center space-x-2">
              <Shield className="w-5 h-5 text-green-400" />
              <span className="text-sm">30-day money back</span>
            </div>
            <div className="flex items-center space-x-2">
              <Infinity className="w-5 h-5 text-cyan-400" />
              <span className="text-sm">Cancel anytime</span>
            </div>
            <div className="flex items-center space-x-2">
              <Star className="w-5 h-5 text-yellow-400" />
              <span className="text-sm">4.9/5 rating</span>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default PricingSection
