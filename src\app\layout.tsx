import type { <PERSON>ada<PERSON> } from "next";
import { Inter, Orbitron, JetBrains_Mono } from "next/font/google";
import "./globals.css";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: "swap",
});

const orbitron = Orbitron({
  variable: "--font-orbitron",
  subsets: ["latin"],
  display: "swap",
});

const jetbrainsMono = JetBrains_Mono({
  variable: "--font-jetbrains-mono",
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "WarrantyAI - Never Miss a Warranty Again",
  description: "Smart AI assistant to track, manage, and remind you of all warranties, services, and coverage across electronics, home, vehicles, and more. Own smart. Live smart.",
  keywords: ["warranty tracking", "AI assistant", "receipt management", "warranty reminder", "smart home", "product tracking"],
  authors: [{ name: "WarrantyAI Team" }],
  creator: "WarrantyAI",
  publisher: "WarrantyAI",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL("https://warrantyai.com"),
  alternates: {
    canonical: "/",
  },
  openGraph: {
    title: "WarrantyAI - Never Miss a Warranty Again",
    description: "Smart AI assistant to track, manage, and remind you of all warranties, services, and coverage. Own smart. Live smart.",
    url: "https://warrantyai.com",
    siteName: "WarrantyAI",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "WarrantyAI - Smart Warranty Tracking",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "WarrantyAI - Never Miss a Warranty Again",
    description: "Smart AI assistant to track, manage, and remind you of all warranties, services, and coverage.",
    images: ["/og-image.jpg"],
    creator: "@warrantyai",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "your-google-verification-code",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <meta name="theme-color" content="#0a0a0a" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5" />
      </head>
      <body
        className={`${inter.variable} ${orbitron.variable} ${jetbrainsMono.variable} font-sans antialiased bg-dark-950 text-white overflow-x-hidden`}
      >
        <div id="root">
          {children}
        </div>
      </body>
    </html>
  );
}
