'use client'

import React, { useState, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Upload, 
  Camera, 
  FileText, 
  Scan, 
  CheckCircle, 
  Bell, 
  Calendar,
  Shield,
  ArrowLeft,
  Download,
  Eye,
  Trash2
} from 'lucide-react'
import <PERSON><PERSON> from '@/components/ui/Button'
import { cn } from '@/lib/utils'
import { storage } from '@/lib/utils'
import Link from 'next/link'

interface WarrantyItem {
  id: string
  name: string
  brand: string
  model: string
  purchaseDate: string
  warrantyExpiry: string
  serialNumber: string
  category: string
  status: 'active' | 'expiring' | 'expired'
  daysLeft: number
  receiptUrl?: string
}

const DemoPage = () => {
  const [step, setStep] = useState(1)
  const [uploadedFile, setUploadedFile] = useState<File | null>(null)
  const [extractedData, setExtractedData] = useState<Partial<WarrantyItem> | null>(null)
  const [warranties, setWarranties] = useState<WarrantyItem[]>(() => {
    // Load from localStorage on client side
    if (typeof window !== 'undefined') {
      return storage.get<WarrantyItem[]>('demo-warranties') || []
    }
    return []
  })
  const [isProcessing, setIsProcessing] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Simulate AI extraction
  const simulateAIExtraction = (file: File) => {
    setIsProcessing(true)
    
    // Simulate processing time
    setTimeout(() => {
      const mockData: Partial<WarrantyItem> = {
        name: file.name.includes('iphone') ? 'iPhone 15 Pro' : 
              file.name.includes('laptop') ? 'MacBook Pro M3' :
              file.name.includes('tv') ? 'Samsung 65" QLED TV' :
              'Sony WH-1000XM5 Headphones',
        brand: file.name.includes('iphone') ? 'Apple' :
               file.name.includes('laptop') ? 'Apple' :
               file.name.includes('tv') ? 'Samsung' :
               'Sony',
        model: file.name.includes('iphone') ? 'A3102' :
               file.name.includes('laptop') ? 'MBP16-M3' :
               file.name.includes('tv') ? 'QN65Q80C' :
               'WH-1000XM5',
        purchaseDate: '2024-01-15',
        warrantyExpiry: '2025-01-15',
        serialNumber: Math.random().toString(36).substr(2, 9).toUpperCase(),
        category: file.name.includes('iphone') || file.name.includes('laptop') ? 'Electronics' :
                  file.name.includes('tv') ? 'Home Entertainment' :
                  'Audio'
      }
      
      setExtractedData(mockData)
      setIsProcessing(false)
      setStep(3)
    }, 2000)
  }

  const handleFileUpload = (file: File) => {
    setUploadedFile(file)
    setStep(2)
    simulateAIExtraction(file)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileUpload(files[0])
    }
  }

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      handleFileUpload(files[0])
    }
  }

  const addToWarranties = () => {
    if (!extractedData) return

    const newWarranty: WarrantyItem = {
      id: Math.random().toString(36).substr(2, 9),
      name: extractedData.name || 'Unknown Product',
      brand: extractedData.brand || 'Unknown Brand',
      model: extractedData.model || 'Unknown Model',
      purchaseDate: extractedData.purchaseDate || new Date().toISOString().split('T')[0],
      warrantyExpiry: extractedData.warrantyExpiry || new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      serialNumber: extractedData.serialNumber || 'N/A',
      category: extractedData.category || 'General',
      status: 'active',
      daysLeft: Math.floor((new Date(extractedData.warrantyExpiry || '').getTime() - Date.now()) / (1000 * 60 * 60 * 24)),
      receiptUrl: uploadedFile ? URL.createObjectURL(uploadedFile) : undefined
    }

    const updatedWarranties = [...warranties, newWarranty]
    setWarranties(updatedWarranties)
    storage.set('demo-warranties', updatedWarranties)
    setStep(4)
  }

  const resetDemo = () => {
    setStep(1)
    setUploadedFile(null)
    setExtractedData(null)
    setIsProcessing(false)
  }

  const deleteWarranty = (id: string) => {
    const updatedWarranties = warranties.filter(w => w.id !== id)
    setWarranties(updatedWarranties)
    storage.set('demo-warranties', updatedWarranties)
  }

  return (
    <div className="min-h-screen bg-dark-950 text-white">
      {/* Header */}
      <div className="border-b border-gray-800 bg-dark-900/50 backdrop-blur-sm sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link href="/">
                <Button variant="glass" size="sm" icon={<ArrowLeft />}>
                  Back to Home
                </Button>
              </Link>
              <div>
                <h1 className="text-2xl font-display font-bold gradient-text">
                  WarrantyAI Demo
                </h1>
                <p className="text-sm text-gray-400">
                  Experience the full warranty tracking workflow
                </p>
              </div>
            </div>
            
            <Button variant="outline" size="sm" onClick={resetDemo}>
              Reset Demo
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Demo Area */}
          <div className="lg:col-span-2">
            <div className="glass-card p-8 min-h-96">
              <AnimatePresence mode="wait">
                {/* Step 1: Upload */}
                {step === 1 && (
                  <motion.div
                    key="upload"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className="text-center"
                  >
                    <h2 className="text-3xl font-bold mb-6">Upload Your Receipt</h2>
                    <p className="text-gray-400 mb-8">
                      Drag and drop a receipt image or click to select a file
                    </p>

                    <div
                      className="border-2 border-dashed border-gray-600 rounded-lg p-12 hover:border-cyan-400 transition-colors duration-300 cursor-pointer"
                      onDrop={handleDrop}
                      onDragOver={(e) => e.preventDefault()}
                      onClick={() => fileInputRef.current?.click()}
                    >
                      <Upload className="w-16 h-16 mx-auto mb-4 text-gray-400" />
                      <p className="text-lg text-gray-300 mb-2">
                        Drop your receipt here
                      </p>
                      <p className="text-sm text-gray-500">
                        Supports JPG, PNG, PDF files
                      </p>
                      
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*,.pdf"
                        onChange={handleFileSelect}
                        className="hidden"
                      />
                    </div>

                    <div className="mt-8 flex justify-center space-x-4">
                      <Button 
                        variant="glass" 
                        icon={<Camera />}
                        onClick={() => fileInputRef.current?.click()}
                      >
                        Choose File
                      </Button>
                      <Button 
                        variant="neon" 
                        icon={<FileText />}
                        onClick={() => {
                          // Simulate file upload for demo
                          const mockFile = new File([''], 'sample-receipt.jpg', { type: 'image/jpeg' })
                          handleFileUpload(mockFile)
                        }}
                      >
                        Try Sample Receipt
                      </Button>
                    </div>
                  </motion.div>
                )}

                {/* Step 2: Processing */}
                {step === 2 && (
                  <motion.div
                    key="processing"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className="text-center"
                  >
                    <h2 className="text-3xl font-bold mb-6">AI Processing</h2>
                    <p className="text-gray-400 mb-8">
                      Our AI is extracting warranty information from your receipt
                    </p>

                    <div className="space-y-6">
                      <motion.div
                        className="w-20 h-20 mx-auto bg-cyan-400/20 border-2 border-cyan-400 rounded-full flex items-center justify-center"
                        animate={{ rotate: 360 }}
                        transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                      >
                        <Scan className="w-8 h-8 text-cyan-400" />
                      </motion.div>

                      <div className="bg-gray-800 rounded-lg p-6">
                        <div className="flex items-center space-x-2 mb-4">
                          <Scan className="w-5 h-5 text-cyan-400" />
                          <span className="text-cyan-400">Analyzing receipt...</span>
                        </div>
                        
                        <div className="space-y-2">
                          {[
                            'Detecting text and layout',
                            'Identifying product information',
                            'Extracting warranty details',
                            'Validating data accuracy'
                          ].map((task, idx) => (
                            <motion.div
                              key={task}
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              transition={{ delay: idx * 0.5 }}
                              className="flex items-center space-x-2"
                            >
                              <CheckCircle className="w-4 h-4 text-green-400" />
                              <span className="text-sm text-gray-300">{task}</span>
                            </motion.div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                )}

                {/* Step 3: Review Data */}
                {step === 3 && extractedData && (
                  <motion.div
                    key="review"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                  >
                    <h2 className="text-3xl font-bold mb-6">Review Extracted Data</h2>
                    <p className="text-gray-400 mb-8">
                      Verify the information extracted by our AI
                    </p>

                    <div className="grid md:grid-cols-2 gap-6 mb-8">
                      {Object.entries(extractedData).map(([key, value]) => (
                        <div key={key} className="bg-gray-800 rounded-lg p-4">
                          <label className="block text-sm text-gray-400 mb-2 capitalize">
                            {key.replace(/([A-Z])/g, ' $1').trim()}
                          </label>
                          <input
                            type={key.includes('Date') ? 'date' : 'text'}
                            value={value || ''}
                            onChange={(e) => setExtractedData({
                              ...extractedData,
                              [key]: e.target.value
                            })}
                            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-cyan-400"
                          />
                        </div>
                      ))}
                    </div>

                    <div className="flex space-x-4">
                      <Button variant="glass" onClick={() => setStep(2)}>
                        Re-process
                      </Button>
                      <Button variant="neon" onClick={addToWarranties} icon={<CheckCircle />}>
                        Add to Dashboard
                      </Button>
                    </div>
                  </motion.div>
                )}

                {/* Step 4: Success */}
                {step === 4 && (
                  <motion.div
                    key="success"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className="text-center"
                  >
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.2, type: "spring" }}
                      className="w-20 h-20 mx-auto bg-green-400/20 border-2 border-green-400 rounded-full flex items-center justify-center mb-6"
                    >
                      <CheckCircle className="w-10 h-10 text-green-400" />
                    </motion.div>

                    <h2 className="text-3xl font-bold mb-4">Warranty Added Successfully!</h2>
                    <p className="text-gray-400 mb-8">
                      Your warranty has been added to your dashboard with smart reminders enabled.
                    </p>

                    <div className="flex justify-center space-x-4">
                      <Button variant="glass" onClick={resetDemo}>
                        Add Another
                      </Button>
                      <Button variant="neon" icon={<Bell />}>
                        Set Reminders
                      </Button>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>

          {/* Warranty Dashboard */}
          <div className="lg:col-span-1">
            <div className="glass-card p-6">
              <h3 className="text-xl font-bold mb-6 flex items-center space-x-2">
                <Shield className="w-5 h-5 text-cyan-400" />
                <span>Your Warranties</span>
              </h3>

              {warranties.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="w-12 h-12 mx-auto mb-4 text-gray-600" />
                  <p className="text-gray-400">No warranties yet</p>
                  <p className="text-sm text-gray-500">Upload a receipt to get started</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {warranties.map((warranty) => (
                    <motion.div
                      key={warranty.id}
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      className="bg-gray-800 rounded-lg p-4"
                    >
                      <div className="flex items-start justify-between mb-2">
                        <div>
                          <h4 className="font-medium text-white">{warranty.name}</h4>
                          <p className="text-sm text-gray-400">{warranty.brand}</p>
                        </div>
                        <button
                          onClick={() => deleteWarranty(warranty.id)}
                          className="text-gray-500 hover:text-red-400 transition-colors"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <span className={cn(
                          "text-xs font-medium px-2 py-1 rounded-full",
                          warranty.daysLeft > 30 ? "bg-green-400/20 text-green-400" :
                          warranty.daysLeft > 0 ? "bg-yellow-400/20 text-yellow-400" :
                          "bg-red-400/20 text-red-400"
                        )}>
                          {warranty.daysLeft > 0 ? `${warranty.daysLeft} days left` : 'Expired'}
                        </span>
                        
                        <div className="flex space-x-2">
                          {warranty.receiptUrl && (
                            <button className="text-gray-400 hover:text-white">
                              <Eye className="w-4 h-4" />
                            </button>
                          )}
                          <button className="text-gray-400 hover:text-white">
                            <Download className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default DemoPage
