/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/handle-element.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/resize/handle-element.mjs ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resizeElement: () => (/* binding */ resizeElement)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/is-svg-element.mjs\");\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/resolve-elements.mjs\");\n\n\nconst resizeHandlers = new WeakMap();\nlet observer;\nfunction getElementSize(target, borderBoxSize) {\n    if (borderBoxSize) {\n        const { inlineSize, blockSize } = borderBoxSize[0];\n        return { width: inlineSize, height: blockSize };\n    }\n    else if ((0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.isSVGElement)(target) && \"getBBox\" in target) {\n        return target.getBBox();\n    }\n    else {\n        return {\n            width: target.offsetWidth,\n            height: target.offsetHeight,\n        };\n    }\n}\nfunction notifyTarget({ target, contentRect, borderBoxSize, }) {\n    resizeHandlers.get(target)?.forEach((handler) => {\n        handler({\n            target,\n            contentSize: contentRect,\n            get size() {\n                return getElementSize(target, borderBoxSize);\n            },\n        });\n    });\n}\nfunction notifyAll(entries) {\n    entries.forEach(notifyTarget);\n}\nfunction createResizeObserver() {\n    if (typeof ResizeObserver === \"undefined\")\n        return;\n    observer = new ResizeObserver(notifyAll);\n}\nfunction resizeElement(target, handler) {\n    if (!observer)\n        createResizeObserver();\n    const elements = (0,motion_dom__WEBPACK_IMPORTED_MODULE_1__.resolveElements)(target);\n    elements.forEach((element) => {\n        let elementHandlers = resizeHandlers.get(element);\n        if (!elementHandlers) {\n            elementHandlers = new Set();\n            resizeHandlers.set(element, elementHandlers);\n        }\n        elementHandlers.add(handler);\n        observer?.observe(element);\n    });\n    return () => {\n        elements.forEach((element) => {\n            const elementHandlers = resizeHandlers.get(element);\n            elementHandlers?.delete(handler);\n            if (!elementHandlers?.size) {\n                observer?.unobserve(element);\n            }\n        });\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9yZXNpemUvaGFuZGxlLWVsZW1lbnQubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUEyRDs7QUFFM0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0Isd0JBQXdCO0FBQ3hDLGlCQUFpQjtBQUNqQjtBQUNBLGFBQWEsd0RBQVk7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLHFDQUFxQztBQUM3RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2IsU0FBUztBQUNULEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUIsMkRBQWU7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBOztBQUV5QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxnb2xkX1xccGpzXFxpNS1kMy13YXJyYW50eWFpXFxub2RlX21vZHVsZXNcXGZyYW1lci1tb3Rpb25cXGRpc3RcXGVzXFxyZW5kZXJcXGRvbVxccmVzaXplXFxoYW5kbGUtZWxlbWVudC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgcmVzb2x2ZUVsZW1lbnRzLCBpc1NWR0VsZW1lbnQgfSBmcm9tICdtb3Rpb24tZG9tJztcblxuY29uc3QgcmVzaXplSGFuZGxlcnMgPSBuZXcgV2Vha01hcCgpO1xubGV0IG9ic2VydmVyO1xuZnVuY3Rpb24gZ2V0RWxlbWVudFNpemUodGFyZ2V0LCBib3JkZXJCb3hTaXplKSB7XG4gICAgaWYgKGJvcmRlckJveFNpemUpIHtcbiAgICAgICAgY29uc3QgeyBpbmxpbmVTaXplLCBibG9ja1NpemUgfSA9IGJvcmRlckJveFNpemVbMF07XG4gICAgICAgIHJldHVybiB7IHdpZHRoOiBpbmxpbmVTaXplLCBoZWlnaHQ6IGJsb2NrU2l6ZSB9O1xuICAgIH1cbiAgICBlbHNlIGlmIChpc1NWR0VsZW1lbnQodGFyZ2V0KSAmJiBcImdldEJCb3hcIiBpbiB0YXJnZXQpIHtcbiAgICAgICAgcmV0dXJuIHRhcmdldC5nZXRCQm94KCk7XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgd2lkdGg6IHRhcmdldC5vZmZzZXRXaWR0aCxcbiAgICAgICAgICAgIGhlaWdodDogdGFyZ2V0Lm9mZnNldEhlaWdodCxcbiAgICAgICAgfTtcbiAgICB9XG59XG5mdW5jdGlvbiBub3RpZnlUYXJnZXQoeyB0YXJnZXQsIGNvbnRlbnRSZWN0LCBib3JkZXJCb3hTaXplLCB9KSB7XG4gICAgcmVzaXplSGFuZGxlcnMuZ2V0KHRhcmdldCk/LmZvckVhY2goKGhhbmRsZXIpID0+IHtcbiAgICAgICAgaGFuZGxlcih7XG4gICAgICAgICAgICB0YXJnZXQsXG4gICAgICAgICAgICBjb250ZW50U2l6ZTogY29udGVudFJlY3QsXG4gICAgICAgICAgICBnZXQgc2l6ZSgpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gZ2V0RWxlbWVudFNpemUodGFyZ2V0LCBib3JkZXJCb3hTaXplKTtcbiAgICAgICAgICAgIH0sXG4gICAgICAgIH0pO1xuICAgIH0pO1xufVxuZnVuY3Rpb24gbm90aWZ5QWxsKGVudHJpZXMpIHtcbiAgICBlbnRyaWVzLmZvckVhY2gobm90aWZ5VGFyZ2V0KTtcbn1cbmZ1bmN0aW9uIGNyZWF0ZVJlc2l6ZU9ic2VydmVyKCkge1xuICAgIGlmICh0eXBlb2YgUmVzaXplT2JzZXJ2ZXIgPT09IFwidW5kZWZpbmVkXCIpXG4gICAgICAgIHJldHVybjtcbiAgICBvYnNlcnZlciA9IG5ldyBSZXNpemVPYnNlcnZlcihub3RpZnlBbGwpO1xufVxuZnVuY3Rpb24gcmVzaXplRWxlbWVudCh0YXJnZXQsIGhhbmRsZXIpIHtcbiAgICBpZiAoIW9ic2VydmVyKVxuICAgICAgICBjcmVhdGVSZXNpemVPYnNlcnZlcigpO1xuICAgIGNvbnN0IGVsZW1lbnRzID0gcmVzb2x2ZUVsZW1lbnRzKHRhcmdldCk7XG4gICAgZWxlbWVudHMuZm9yRWFjaCgoZWxlbWVudCkgPT4ge1xuICAgICAgICBsZXQgZWxlbWVudEhhbmRsZXJzID0gcmVzaXplSGFuZGxlcnMuZ2V0KGVsZW1lbnQpO1xuICAgICAgICBpZiAoIWVsZW1lbnRIYW5kbGVycykge1xuICAgICAgICAgICAgZWxlbWVudEhhbmRsZXJzID0gbmV3IFNldCgpO1xuICAgICAgICAgICAgcmVzaXplSGFuZGxlcnMuc2V0KGVsZW1lbnQsIGVsZW1lbnRIYW5kbGVycyk7XG4gICAgICAgIH1cbiAgICAgICAgZWxlbWVudEhhbmRsZXJzLmFkZChoYW5kbGVyKTtcbiAgICAgICAgb2JzZXJ2ZXI/Lm9ic2VydmUoZWxlbWVudCk7XG4gICAgfSk7XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgICAgZWxlbWVudHMuZm9yRWFjaCgoZWxlbWVudCkgPT4ge1xuICAgICAgICAgICAgY29uc3QgZWxlbWVudEhhbmRsZXJzID0gcmVzaXplSGFuZGxlcnMuZ2V0KGVsZW1lbnQpO1xuICAgICAgICAgICAgZWxlbWVudEhhbmRsZXJzPy5kZWxldGUoaGFuZGxlcik7XG4gICAgICAgICAgICBpZiAoIWVsZW1lbnRIYW5kbGVycz8uc2l6ZSkge1xuICAgICAgICAgICAgICAgIG9ic2VydmVyPy51bm9ic2VydmUoZWxlbWVudCk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgIH07XG59XG5cbmV4cG9ydCB7IHJlc2l6ZUVsZW1lbnQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/handle-element.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/handle-window.mjs":
/*!********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/resize/handle-window.mjs ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resizeWindow: () => (/* binding */ resizeWindow)\n/* harmony export */ });\nconst windowCallbacks = new Set();\nlet windowResizeHandler;\nfunction createWindowResizeHandler() {\n    windowResizeHandler = () => {\n        const size = {\n            width: window.innerWidth,\n            height: window.innerHeight,\n        };\n        const info = {\n            target: window,\n            size,\n            contentSize: size,\n        };\n        windowCallbacks.forEach((callback) => callback(info));\n    };\n    window.addEventListener(\"resize\", windowResizeHandler);\n}\nfunction resizeWindow(callback) {\n    windowCallbacks.add(callback);\n    if (!windowResizeHandler)\n        createWindowResizeHandler();\n    return () => {\n        windowCallbacks.delete(callback);\n        if (!windowCallbacks.size && windowResizeHandler) {\n            windowResizeHandler = undefined;\n        }\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9yZXNpemUvaGFuZGxlLXdpbmRvdy5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUV3QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxnb2xkX1xccGpzXFxpNS1kMy13YXJyYW50eWFpXFxub2RlX21vZHVsZXNcXGZyYW1lci1tb3Rpb25cXGRpc3RcXGVzXFxyZW5kZXJcXGRvbVxccmVzaXplXFxoYW5kbGUtd2luZG93Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCB3aW5kb3dDYWxsYmFja3MgPSBuZXcgU2V0KCk7XG5sZXQgd2luZG93UmVzaXplSGFuZGxlcjtcbmZ1bmN0aW9uIGNyZWF0ZVdpbmRvd1Jlc2l6ZUhhbmRsZXIoKSB7XG4gICAgd2luZG93UmVzaXplSGFuZGxlciA9ICgpID0+IHtcbiAgICAgICAgY29uc3Qgc2l6ZSA9IHtcbiAgICAgICAgICAgIHdpZHRoOiB3aW5kb3cuaW5uZXJXaWR0aCxcbiAgICAgICAgICAgIGhlaWdodDogd2luZG93LmlubmVySGVpZ2h0LFxuICAgICAgICB9O1xuICAgICAgICBjb25zdCBpbmZvID0ge1xuICAgICAgICAgICAgdGFyZ2V0OiB3aW5kb3csXG4gICAgICAgICAgICBzaXplLFxuICAgICAgICAgICAgY29udGVudFNpemU6IHNpemUsXG4gICAgICAgIH07XG4gICAgICAgIHdpbmRvd0NhbGxiYWNrcy5mb3JFYWNoKChjYWxsYmFjaykgPT4gY2FsbGJhY2soaW5mbykpO1xuICAgIH07XG4gICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoXCJyZXNpemVcIiwgd2luZG93UmVzaXplSGFuZGxlcik7XG59XG5mdW5jdGlvbiByZXNpemVXaW5kb3coY2FsbGJhY2spIHtcbiAgICB3aW5kb3dDYWxsYmFja3MuYWRkKGNhbGxiYWNrKTtcbiAgICBpZiAoIXdpbmRvd1Jlc2l6ZUhhbmRsZXIpXG4gICAgICAgIGNyZWF0ZVdpbmRvd1Jlc2l6ZUhhbmRsZXIoKTtcbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgICB3aW5kb3dDYWxsYmFja3MuZGVsZXRlKGNhbGxiYWNrKTtcbiAgICAgICAgaWYgKCF3aW5kb3dDYWxsYmFja3Muc2l6ZSAmJiB3aW5kb3dSZXNpemVIYW5kbGVyKSB7XG4gICAgICAgICAgICB3aW5kb3dSZXNpemVIYW5kbGVyID0gdW5kZWZpbmVkO1xuICAgICAgICB9XG4gICAgfTtcbn1cblxuZXhwb3J0IHsgcmVzaXplV2luZG93IH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/handle-window.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/index.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/resize/index.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resize: () => (/* binding */ resize)\n/* harmony export */ });\n/* harmony import */ var _handle_element_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./handle-element.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/handle-element.mjs\");\n/* harmony import */ var _handle_window_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./handle-window.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/handle-window.mjs\");\n\n\n\nfunction resize(a, b) {\n    return typeof a === \"function\" ? (0,_handle_window_mjs__WEBPACK_IMPORTED_MODULE_0__.resizeWindow)(a) : (0,_handle_element_mjs__WEBPACK_IMPORTED_MODULE_1__.resizeElement)(a, b);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9yZXNpemUvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFxRDtBQUNGOztBQUVuRDtBQUNBLHFDQUFxQyxnRUFBWSxNQUFNLGtFQUFhO0FBQ3BFOztBQUVrQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxnb2xkX1xccGpzXFxpNS1kMy13YXJyYW50eWFpXFxub2RlX21vZHVsZXNcXGZyYW1lci1tb3Rpb25cXGRpc3RcXGVzXFxyZW5kZXJcXGRvbVxccmVzaXplXFxpbmRleC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgcmVzaXplRWxlbWVudCB9IGZyb20gJy4vaGFuZGxlLWVsZW1lbnQubWpzJztcbmltcG9ydCB7IHJlc2l6ZVdpbmRvdyB9IGZyb20gJy4vaGFuZGxlLXdpbmRvdy5tanMnO1xuXG5mdW5jdGlvbiByZXNpemUoYSwgYikge1xuICAgIHJldHVybiB0eXBlb2YgYSA9PT0gXCJmdW5jdGlvblwiID8gcmVzaXplV2luZG93KGEpIDogcmVzaXplRWxlbWVudChhLCBiKTtcbn1cblxuZXhwb3J0IHsgcmVzaXplIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/attach-animation.mjs":
/*!***********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/attach-animation.mjs ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attachToAnimation: () => (/* binding */ attachToAnimation)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/scroll/observe.mjs\");\n/* harmony import */ var _utils_get_timeline_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/get-timeline.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/utils/get-timeline.mjs\");\n\n\n\nfunction attachToAnimation(animation, options) {\n    const timeline = (0,_utils_get_timeline_mjs__WEBPACK_IMPORTED_MODULE_0__.getTimeline)(options);\n    return animation.attachTimeline({\n        timeline: options.target ? undefined : timeline,\n        observe: (valueAnimation) => {\n            valueAnimation.pause();\n            return (0,motion_dom__WEBPACK_IMPORTED_MODULE_1__.observeTimeline)((progress) => {\n                valueAnimation.time = valueAnimation.duration * progress;\n            }, timeline);\n        },\n    });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9zY3JvbGwvYXR0YWNoLWFuaW1hdGlvbi5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTZDO0FBQ1U7O0FBRXZEO0FBQ0EscUJBQXFCLG9FQUFXO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLDJEQUFlO0FBQ2xDO0FBQ0EsYUFBYTtBQUNiLFNBQVM7QUFDVCxLQUFLO0FBQ0w7O0FBRTZCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGdvbGRfXFxwanNcXGk1LWQzLXdhcnJhbnR5YWlcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXHJlbmRlclxcZG9tXFxzY3JvbGxcXGF0dGFjaC1hbmltYXRpb24ubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IG9ic2VydmVUaW1lbGluZSB9IGZyb20gJ21vdGlvbi1kb20nO1xuaW1wb3J0IHsgZ2V0VGltZWxpbmUgfSBmcm9tICcuL3V0aWxzL2dldC10aW1lbGluZS5tanMnO1xuXG5mdW5jdGlvbiBhdHRhY2hUb0FuaW1hdGlvbihhbmltYXRpb24sIG9wdGlvbnMpIHtcbiAgICBjb25zdCB0aW1lbGluZSA9IGdldFRpbWVsaW5lKG9wdGlvbnMpO1xuICAgIHJldHVybiBhbmltYXRpb24uYXR0YWNoVGltZWxpbmUoe1xuICAgICAgICB0aW1lbGluZTogb3B0aW9ucy50YXJnZXQgPyB1bmRlZmluZWQgOiB0aW1lbGluZSxcbiAgICAgICAgb2JzZXJ2ZTogKHZhbHVlQW5pbWF0aW9uKSA9PiB7XG4gICAgICAgICAgICB2YWx1ZUFuaW1hdGlvbi5wYXVzZSgpO1xuICAgICAgICAgICAgcmV0dXJuIG9ic2VydmVUaW1lbGluZSgocHJvZ3Jlc3MpID0+IHtcbiAgICAgICAgICAgICAgICB2YWx1ZUFuaW1hdGlvbi50aW1lID0gdmFsdWVBbmltYXRpb24uZHVyYXRpb24gKiBwcm9ncmVzcztcbiAgICAgICAgICAgIH0sIHRpbWVsaW5lKTtcbiAgICAgICAgfSxcbiAgICB9KTtcbn1cblxuZXhwb3J0IHsgYXR0YWNoVG9BbmltYXRpb24gfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/attach-animation.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/attach-function.mjs":
/*!**********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/attach-function.mjs ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attachToFunction: () => (/* binding */ attachToFunction)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/scroll/observe.mjs\");\n/* harmony import */ var _track_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./track.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs\");\n/* harmony import */ var _utils_get_timeline_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/get-timeline.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/utils/get-timeline.mjs\");\n\n\n\n\n/**\n * If the onScroll function has two arguments, it's expecting\n * more specific information about the scroll from scrollInfo.\n */\nfunction isOnScrollWithInfo(onScroll) {\n    return onScroll.length === 2;\n}\nfunction attachToFunction(onScroll, options) {\n    if (isOnScrollWithInfo(onScroll)) {\n        return (0,_track_mjs__WEBPACK_IMPORTED_MODULE_0__.scrollInfo)((info) => {\n            onScroll(info[options.axis].progress, info);\n        }, options);\n    }\n    else {\n        return (0,motion_dom__WEBPACK_IMPORTED_MODULE_1__.observeTimeline)(onScroll, (0,_utils_get_timeline_mjs__WEBPACK_IMPORTED_MODULE_2__.getTimeline)(options));\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9zY3JvbGwvYXR0YWNoLWZ1bmN0aW9uLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTZDO0FBQ0o7QUFDYzs7QUFFdkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxzREFBVTtBQUN6QjtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsZUFBZSwyREFBZSxXQUFXLG9FQUFXO0FBQ3BEO0FBQ0E7O0FBRTRCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGdvbGRfXFxwanNcXGk1LWQzLXdhcnJhbnR5YWlcXG5vZGVfbW9kdWxlc1xcZnJhbWVyLW1vdGlvblxcZGlzdFxcZXNcXHJlbmRlclxcZG9tXFxzY3JvbGxcXGF0dGFjaC1mdW5jdGlvbi5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgb2JzZXJ2ZVRpbWVsaW5lIH0gZnJvbSAnbW90aW9uLWRvbSc7XG5pbXBvcnQgeyBzY3JvbGxJbmZvIH0gZnJvbSAnLi90cmFjay5tanMnO1xuaW1wb3J0IHsgZ2V0VGltZWxpbmUgfSBmcm9tICcuL3V0aWxzL2dldC10aW1lbGluZS5tanMnO1xuXG4vKipcbiAqIElmIHRoZSBvblNjcm9sbCBmdW5jdGlvbiBoYXMgdHdvIGFyZ3VtZW50cywgaXQncyBleHBlY3RpbmdcbiAqIG1vcmUgc3BlY2lmaWMgaW5mb3JtYXRpb24gYWJvdXQgdGhlIHNjcm9sbCBmcm9tIHNjcm9sbEluZm8uXG4gKi9cbmZ1bmN0aW9uIGlzT25TY3JvbGxXaXRoSW5mbyhvblNjcm9sbCkge1xuICAgIHJldHVybiBvblNjcm9sbC5sZW5ndGggPT09IDI7XG59XG5mdW5jdGlvbiBhdHRhY2hUb0Z1bmN0aW9uKG9uU2Nyb2xsLCBvcHRpb25zKSB7XG4gICAgaWYgKGlzT25TY3JvbGxXaXRoSW5mbyhvblNjcm9sbCkpIHtcbiAgICAgICAgcmV0dXJuIHNjcm9sbEluZm8oKGluZm8pID0+IHtcbiAgICAgICAgICAgIG9uU2Nyb2xsKGluZm9bb3B0aW9ucy5heGlzXS5wcm9ncmVzcywgaW5mbyk7XG4gICAgICAgIH0sIG9wdGlvbnMpO1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgcmV0dXJuIG9ic2VydmVUaW1lbGluZShvblNjcm9sbCwgZ2V0VGltZWxpbmUob3B0aW9ucykpO1xuICAgIH1cbn1cblxuZXhwb3J0IHsgYXR0YWNoVG9GdW5jdGlvbiB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/attach-function.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/index.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/index.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   scroll: () => (/* binding */ scroll)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/noop.mjs\");\n/* harmony import */ var _attach_animation_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./attach-animation.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/attach-animation.mjs\");\n/* harmony import */ var _attach_function_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./attach-function.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/attach-function.mjs\");\n\n\n\n\nfunction scroll(onScroll, { axis = \"y\", container = document.scrollingElement, ...options } = {}) {\n    if (!container)\n        return motion_utils__WEBPACK_IMPORTED_MODULE_0__.noop;\n    const optionsWithDefaults = { axis, container, ...options };\n    return typeof onScroll === \"function\"\n        ? (0,_attach_function_mjs__WEBPACK_IMPORTED_MODULE_1__.attachToFunction)(onScroll, optionsWithDefaults)\n        : (0,_attach_animation_mjs__WEBPACK_IMPORTED_MODULE_2__.attachToAnimation)(onScroll, optionsWithDefaults);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9zY3JvbGwvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBb0M7QUFDdUI7QUFDRjs7QUFFekQsNEJBQTRCLGdFQUFnRSxJQUFJO0FBQ2hHO0FBQ0EsZUFBZSw4Q0FBSTtBQUNuQixrQ0FBa0M7QUFDbEM7QUFDQSxVQUFVLHNFQUFnQjtBQUMxQixVQUFVLHdFQUFpQjtBQUMzQjs7QUFFa0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZ29sZF9cXHBqc1xcaTUtZDMtd2FycmFudHlhaVxcbm9kZV9tb2R1bGVzXFxmcmFtZXItbW90aW9uXFxkaXN0XFxlc1xccmVuZGVyXFxkb21cXHNjcm9sbFxcaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IG5vb3AgfSBmcm9tICdtb3Rpb24tdXRpbHMnO1xuaW1wb3J0IHsgYXR0YWNoVG9BbmltYXRpb24gfSBmcm9tICcuL2F0dGFjaC1hbmltYXRpb24ubWpzJztcbmltcG9ydCB7IGF0dGFjaFRvRnVuY3Rpb24gfSBmcm9tICcuL2F0dGFjaC1mdW5jdGlvbi5tanMnO1xuXG5mdW5jdGlvbiBzY3JvbGwob25TY3JvbGwsIHsgYXhpcyA9IFwieVwiLCBjb250YWluZXIgPSBkb2N1bWVudC5zY3JvbGxpbmdFbGVtZW50LCAuLi5vcHRpb25zIH0gPSB7fSkge1xuICAgIGlmICghY29udGFpbmVyKVxuICAgICAgICByZXR1cm4gbm9vcDtcbiAgICBjb25zdCBvcHRpb25zV2l0aERlZmF1bHRzID0geyBheGlzLCBjb250YWluZXIsIC4uLm9wdGlvbnMgfTtcbiAgICByZXR1cm4gdHlwZW9mIG9uU2Nyb2xsID09PSBcImZ1bmN0aW9uXCJcbiAgICAgICAgPyBhdHRhY2hUb0Z1bmN0aW9uKG9uU2Nyb2xsLCBvcHRpb25zV2l0aERlZmF1bHRzKVxuICAgICAgICA6IGF0dGFjaFRvQW5pbWF0aW9uKG9uU2Nyb2xsLCBvcHRpb25zV2l0aERlZmF1bHRzKTtcbn1cblxuZXhwb3J0IHsgc2Nyb2xsIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createScrollInfo: () => (/* binding */ createScrollInfo),\n/* harmony export */   updateScrollInfo: () => (/* binding */ updateScrollInfo)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/progress.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/velocity-per-second.mjs\");\n\n\n/**\n * A time in milliseconds, beyond which we consider the scroll velocity to be 0.\n */\nconst maxElapsed = 50;\nconst createAxisInfo = () => ({\n    current: 0,\n    offset: [],\n    progress: 0,\n    scrollLength: 0,\n    targetOffset: 0,\n    targetLength: 0,\n    containerLength: 0,\n    velocity: 0,\n});\nconst createScrollInfo = () => ({\n    time: 0,\n    x: createAxisInfo(),\n    y: createAxisInfo(),\n});\nconst keys = {\n    x: {\n        length: \"Width\",\n        position: \"Left\",\n    },\n    y: {\n        length: \"Height\",\n        position: \"Top\",\n    },\n};\nfunction updateAxisInfo(element, axisName, info, time) {\n    const axis = info[axisName];\n    const { length, position } = keys[axisName];\n    const prev = axis.current;\n    const prevTime = info.time;\n    axis.current = element[`scroll${position}`];\n    axis.scrollLength = element[`scroll${length}`] - element[`client${length}`];\n    axis.offset.length = 0;\n    axis.offset[0] = 0;\n    axis.offset[1] = axis.scrollLength;\n    axis.progress = (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.progress)(0, axis.scrollLength, axis.current);\n    const elapsed = time - prevTime;\n    axis.velocity =\n        elapsed > maxElapsed\n            ? 0\n            : (0,motion_utils__WEBPACK_IMPORTED_MODULE_1__.velocityPerSecond)(axis.current - prev, elapsed);\n}\nfunction updateScrollInfo(element, info, time) {\n    updateAxisInfo(element, \"x\", info, time);\n    updateAxisInfo(element, \"y\", info, time);\n    info.time = time;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/edge.mjs":
/*!*******************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/edge.mjs ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   namedEdges: () => (/* binding */ namedEdges),\n/* harmony export */   resolveEdge: () => (/* binding */ resolveEdge)\n/* harmony export */ });\nconst namedEdges = {\n    start: 0,\n    center: 0.5,\n    end: 1,\n};\nfunction resolveEdge(edge, length, inset = 0) {\n    let delta = 0;\n    /**\n     * If we have this edge defined as a preset, replace the definition\n     * with the numerical value.\n     */\n    if (edge in namedEdges) {\n        edge = namedEdges[edge];\n    }\n    /**\n     * Handle unit values\n     */\n    if (typeof edge === \"string\") {\n        const asNumber = parseFloat(edge);\n        if (edge.endsWith(\"px\")) {\n            delta = asNumber;\n        }\n        else if (edge.endsWith(\"%\")) {\n            edge = asNumber / 100;\n        }\n        else if (edge.endsWith(\"vw\")) {\n            delta = (asNumber / 100) * document.documentElement.clientWidth;\n        }\n        else if (edge.endsWith(\"vh\")) {\n            delta = (asNumber / 100) * document.documentElement.clientHeight;\n        }\n        else {\n            edge = asNumber;\n        }\n    }\n    /**\n     * If the edge is defined as a number, handle as a progress value.\n     */\n    if (typeof edge === \"number\") {\n        delta = length * edge;\n    }\n    return inset + delta;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/edge.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/index.mjs":
/*!********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/index.mjs ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveOffsets: () => (/* binding */ resolveOffsets)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/interpolate.mjs\");\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/animation/keyframes/offsets/default.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/clamp.mjs\");\n/* harmony import */ var _inset_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./inset.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/inset.mjs\");\n/* harmony import */ var _offset_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./offset.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/offset.mjs\");\n/* harmony import */ var _presets_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./presets.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/presets.mjs\");\n\n\n\n\n\n\nconst point = { x: 0, y: 0 };\nfunction getTargetSize(target) {\n    return \"getBBox\" in target && target.tagName !== \"svg\"\n        ? target.getBBox()\n        : { width: target.clientWidth, height: target.clientHeight };\n}\nfunction resolveOffsets(container, info, options) {\n    const { offset: offsetDefinition = _presets_mjs__WEBPACK_IMPORTED_MODULE_0__.ScrollOffset.All } = options;\n    const { target = container, axis = \"y\" } = options;\n    const lengthLabel = axis === \"y\" ? \"height\" : \"width\";\n    const inset = target !== container ? (0,_inset_mjs__WEBPACK_IMPORTED_MODULE_1__.calcInset)(target, container) : point;\n    /**\n     * Measure the target and container. If they're the same thing then we\n     * use the container's scrollWidth/Height as the target, from there\n     * all other calculations can remain the same.\n     */\n    const targetSize = target === container\n        ? { width: container.scrollWidth, height: container.scrollHeight }\n        : getTargetSize(target);\n    const containerSize = {\n        width: container.clientWidth,\n        height: container.clientHeight,\n    };\n    /**\n     * Reset the length of the resolved offset array rather than creating a new one.\n     * TODO: More reusable data structures for targetSize/containerSize would also be good.\n     */\n    info[axis].offset.length = 0;\n    /**\n     * Populate the offset array by resolving the user's offset definition into\n     * a list of pixel scroll offets.\n     */\n    let hasChanged = !info[axis].interpolate;\n    const numOffsets = offsetDefinition.length;\n    for (let i = 0; i < numOffsets; i++) {\n        const offset = (0,_offset_mjs__WEBPACK_IMPORTED_MODULE_2__.resolveOffset)(offsetDefinition[i], containerSize[lengthLabel], targetSize[lengthLabel], inset[axis]);\n        if (!hasChanged && offset !== info[axis].interpolatorOffsets[i]) {\n            hasChanged = true;\n        }\n        info[axis].offset[i] = offset;\n    }\n    /**\n     * If the pixel scroll offsets have changed, create a new interpolator function\n     * to map scroll value into a progress.\n     */\n    if (hasChanged) {\n        info[axis].interpolate = (0,motion_dom__WEBPACK_IMPORTED_MODULE_3__.interpolate)(info[axis].offset, (0,motion_dom__WEBPACK_IMPORTED_MODULE_4__.defaultOffset)(offsetDefinition), { clamp: false });\n        info[axis].interpolatorOffsets = [...info[axis].offset];\n    }\n    info[axis].progress = (0,motion_utils__WEBPACK_IMPORTED_MODULE_5__.clamp)(0, 1, info[axis].interpolate(info[axis].current));\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/inset.mjs":
/*!********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/inset.mjs ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calcInset: () => (/* binding */ calcInset)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/is-html-element.mjs\");\n\n\nfunction calcInset(element, container) {\n    const inset = { x: 0, y: 0 };\n    let current = element;\n    while (current && current !== container) {\n        if ((0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(current)) {\n            inset.x += current.offsetLeft;\n            inset.y += current.offsetTop;\n            current = current.offsetParent;\n        }\n        else if (current.tagName === \"svg\") {\n            /**\n             * This isn't an ideal approach to measuring the offset of <svg /> tags.\n             * It would be preferable, given they behave like HTMLElements in most ways\n             * to use offsetLeft/Top. But these don't exist on <svg />. Likewise we\n             * can't use .getBBox() like most SVG elements as these provide the offset\n             * relative to the SVG itself, which for <svg /> is usually 0x0.\n             */\n            const svgBoundingBox = current.getBoundingClientRect();\n            current = current.parentElement;\n            const parentBoundingBox = current.getBoundingClientRect();\n            inset.x += svgBoundingBox.left - parentBoundingBox.left;\n            inset.y += svgBoundingBox.top - parentBoundingBox.top;\n        }\n        else if (current instanceof SVGGraphicsElement) {\n            const { x, y } = current.getBBox();\n            inset.x += x;\n            inset.y += y;\n            let svg = null;\n            let parent = current.parentNode;\n            while (!svg) {\n                if (parent.tagName === \"svg\") {\n                    svg = parent;\n                }\n                parent = current.parentNode;\n            }\n            current = svg;\n        }\n        else {\n            break;\n        }\n    }\n    return inset;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/inset.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/offset.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/offset.mjs ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveOffset: () => (/* binding */ resolveOffset)\n/* harmony export */ });\n/* harmony import */ var _edge_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./edge.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/edge.mjs\");\n\n\nconst defaultOffset = [0, 0];\nfunction resolveOffset(offset, containerLength, targetLength, targetInset) {\n    let offsetDefinition = Array.isArray(offset) ? offset : defaultOffset;\n    let targetPoint = 0;\n    let containerPoint = 0;\n    if (typeof offset === \"number\") {\n        /**\n         * If we're provided offset: [0, 0.5, 1] then each number x should become\n         * [x, x], so we default to the behaviour of mapping 0 => 0 of both target\n         * and container etc.\n         */\n        offsetDefinition = [offset, offset];\n    }\n    else if (typeof offset === \"string\") {\n        offset = offset.trim();\n        if (offset.includes(\" \")) {\n            offsetDefinition = offset.split(\" \");\n        }\n        else {\n            /**\n             * If we're provided a definition like \"100px\" then we want to apply\n             * that only to the top of the target point, leaving the container at 0.\n             * Whereas a named offset like \"end\" should be applied to both.\n             */\n            offsetDefinition = [offset, _edge_mjs__WEBPACK_IMPORTED_MODULE_0__.namedEdges[offset] ? offset : `0`];\n        }\n    }\n    targetPoint = (0,_edge_mjs__WEBPACK_IMPORTED_MODULE_0__.resolveEdge)(offsetDefinition[0], targetLength, targetInset);\n    containerPoint = (0,_edge_mjs__WEBPACK_IMPORTED_MODULE_0__.resolveEdge)(offsetDefinition[1], containerLength);\n    return targetPoint - containerPoint;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/offset.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/presets.mjs":
/*!**********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/presets.mjs ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ScrollOffset: () => (/* binding */ ScrollOffset)\n/* harmony export */ });\nconst ScrollOffset = {\n    Enter: [\n        [0, 1],\n        [1, 1],\n    ],\n    Exit: [\n        [0, 0],\n        [1, 0],\n    ],\n    Any: [\n        [1, 0],\n        [0, 1],\n    ],\n    All: [\n        [0, 0],\n        [1, 1],\n    ],\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9zY3JvbGwvb2Zmc2V0cy9wcmVzZXRzLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUV3QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxnb2xkX1xccGpzXFxpNS1kMy13YXJyYW50eWFpXFxub2RlX21vZHVsZXNcXGZyYW1lci1tb3Rpb25cXGRpc3RcXGVzXFxyZW5kZXJcXGRvbVxcc2Nyb2xsXFxvZmZzZXRzXFxwcmVzZXRzLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBTY3JvbGxPZmZzZXQgPSB7XG4gICAgRW50ZXI6IFtcbiAgICAgICAgWzAsIDFdLFxuICAgICAgICBbMSwgMV0sXG4gICAgXSxcbiAgICBFeGl0OiBbXG4gICAgICAgIFswLCAwXSxcbiAgICAgICAgWzEsIDBdLFxuICAgIF0sXG4gICAgQW55OiBbXG4gICAgICAgIFsxLCAwXSxcbiAgICAgICAgWzAsIDFdLFxuICAgIF0sXG4gICAgQWxsOiBbXG4gICAgICAgIFswLCAwXSxcbiAgICAgICAgWzEsIDFdLFxuICAgIF0sXG59O1xuXG5leHBvcnQgeyBTY3JvbGxPZmZzZXQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/presets.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/on-scroll-handler.mjs":
/*!************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/on-scroll-handler.mjs ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createOnScrollHandler: () => (/* binding */ createOnScrollHandler)\n/* harmony export */ });\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/warn-once.mjs\");\n/* harmony import */ var _info_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./info.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs\");\n/* harmony import */ var _offsets_index_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./offsets/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/offsets/index.mjs\");\n\n\n\n\nfunction measure(container, target = container, info) {\n    /**\n     * Find inset of target within scrollable container\n     */\n    info.x.targetOffset = 0;\n    info.y.targetOffset = 0;\n    if (target !== container) {\n        let node = target;\n        while (node && node !== container) {\n            info.x.targetOffset += node.offsetLeft;\n            info.y.targetOffset += node.offsetTop;\n            node = node.offsetParent;\n        }\n    }\n    info.x.targetLength =\n        target === container ? target.scrollWidth : target.clientWidth;\n    info.y.targetLength =\n        target === container ? target.scrollHeight : target.clientHeight;\n    info.x.containerLength = container.clientWidth;\n    info.y.containerLength = container.clientHeight;\n    /**\n     * In development mode ensure scroll containers aren't position: static as this makes\n     * it difficult to measure their relative positions.\n     */\n    if (true) {\n        if (container && target && target !== container) {\n            (0,motion_utils__WEBPACK_IMPORTED_MODULE_0__.warnOnce)(getComputedStyle(container).position !== \"static\", \"Please ensure that the container has a non-static position, like 'relative', 'fixed', or 'absolute' to ensure scroll offset is calculated correctly.\");\n        }\n    }\n}\nfunction createOnScrollHandler(element, onScroll, info, options = {}) {\n    return {\n        measure: (time) => {\n            measure(element, options.target, info);\n            (0,_info_mjs__WEBPACK_IMPORTED_MODULE_1__.updateScrollInfo)(element, info, time);\n            if (options.offset || options.target) {\n                (0,_offsets_index_mjs__WEBPACK_IMPORTED_MODULE_2__.resolveOffsets)(element, info, options);\n            }\n        },\n        notify: () => onScroll(info),\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9zY3JvbGwvb24tc2Nyb2xsLWhhbmRsZXIubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBd0M7QUFDTTtBQUNPOztBQUVyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLElBQXFDO0FBQzdDO0FBQ0EsWUFBWSxzREFBUTtBQUNwQjtBQUNBO0FBQ0E7QUFDQSxvRUFBb0U7QUFDcEU7QUFDQTtBQUNBO0FBQ0EsWUFBWSwyREFBZ0I7QUFDNUI7QUFDQSxnQkFBZ0Isa0VBQWM7QUFDOUI7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBOztBQUVpQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxnb2xkX1xccGpzXFxpNS1kMy13YXJyYW50eWFpXFxub2RlX21vZHVsZXNcXGZyYW1lci1tb3Rpb25cXGRpc3RcXGVzXFxyZW5kZXJcXGRvbVxcc2Nyb2xsXFxvbi1zY3JvbGwtaGFuZGxlci5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgd2Fybk9uY2UgfSBmcm9tICdtb3Rpb24tdXRpbHMnO1xuaW1wb3J0IHsgdXBkYXRlU2Nyb2xsSW5mbyB9IGZyb20gJy4vaW5mby5tanMnO1xuaW1wb3J0IHsgcmVzb2x2ZU9mZnNldHMgfSBmcm9tICcuL29mZnNldHMvaW5kZXgubWpzJztcblxuZnVuY3Rpb24gbWVhc3VyZShjb250YWluZXIsIHRhcmdldCA9IGNvbnRhaW5lciwgaW5mbykge1xuICAgIC8qKlxuICAgICAqIEZpbmQgaW5zZXQgb2YgdGFyZ2V0IHdpdGhpbiBzY3JvbGxhYmxlIGNvbnRhaW5lclxuICAgICAqL1xuICAgIGluZm8ueC50YXJnZXRPZmZzZXQgPSAwO1xuICAgIGluZm8ueS50YXJnZXRPZmZzZXQgPSAwO1xuICAgIGlmICh0YXJnZXQgIT09IGNvbnRhaW5lcikge1xuICAgICAgICBsZXQgbm9kZSA9IHRhcmdldDtcbiAgICAgICAgd2hpbGUgKG5vZGUgJiYgbm9kZSAhPT0gY29udGFpbmVyKSB7XG4gICAgICAgICAgICBpbmZvLngudGFyZ2V0T2Zmc2V0ICs9IG5vZGUub2Zmc2V0TGVmdDtcbiAgICAgICAgICAgIGluZm8ueS50YXJnZXRPZmZzZXQgKz0gbm9kZS5vZmZzZXRUb3A7XG4gICAgICAgICAgICBub2RlID0gbm9kZS5vZmZzZXRQYXJlbnQ7XG4gICAgICAgIH1cbiAgICB9XG4gICAgaW5mby54LnRhcmdldExlbmd0aCA9XG4gICAgICAgIHRhcmdldCA9PT0gY29udGFpbmVyID8gdGFyZ2V0LnNjcm9sbFdpZHRoIDogdGFyZ2V0LmNsaWVudFdpZHRoO1xuICAgIGluZm8ueS50YXJnZXRMZW5ndGggPVxuICAgICAgICB0YXJnZXQgPT09IGNvbnRhaW5lciA/IHRhcmdldC5zY3JvbGxIZWlnaHQgOiB0YXJnZXQuY2xpZW50SGVpZ2h0O1xuICAgIGluZm8ueC5jb250YWluZXJMZW5ndGggPSBjb250YWluZXIuY2xpZW50V2lkdGg7XG4gICAgaW5mby55LmNvbnRhaW5lckxlbmd0aCA9IGNvbnRhaW5lci5jbGllbnRIZWlnaHQ7XG4gICAgLyoqXG4gICAgICogSW4gZGV2ZWxvcG1lbnQgbW9kZSBlbnN1cmUgc2Nyb2xsIGNvbnRhaW5lcnMgYXJlbid0IHBvc2l0aW9uOiBzdGF0aWMgYXMgdGhpcyBtYWtlc1xuICAgICAqIGl0IGRpZmZpY3VsdCB0byBtZWFzdXJlIHRoZWlyIHJlbGF0aXZlIHBvc2l0aW9ucy5cbiAgICAgKi9cbiAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09IFwicHJvZHVjdGlvblwiKSB7XG4gICAgICAgIGlmIChjb250YWluZXIgJiYgdGFyZ2V0ICYmIHRhcmdldCAhPT0gY29udGFpbmVyKSB7XG4gICAgICAgICAgICB3YXJuT25jZShnZXRDb21wdXRlZFN0eWxlKGNvbnRhaW5lcikucG9zaXRpb24gIT09IFwic3RhdGljXCIsIFwiUGxlYXNlIGVuc3VyZSB0aGF0IHRoZSBjb250YWluZXIgaGFzIGEgbm9uLXN0YXRpYyBwb3NpdGlvbiwgbGlrZSAncmVsYXRpdmUnLCAnZml4ZWQnLCBvciAnYWJzb2x1dGUnIHRvIGVuc3VyZSBzY3JvbGwgb2Zmc2V0IGlzIGNhbGN1bGF0ZWQgY29ycmVjdGx5LlwiKTtcbiAgICAgICAgfVxuICAgIH1cbn1cbmZ1bmN0aW9uIGNyZWF0ZU9uU2Nyb2xsSGFuZGxlcihlbGVtZW50LCBvblNjcm9sbCwgaW5mbywgb3B0aW9ucyA9IHt9KSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgbWVhc3VyZTogKHRpbWUpID0+IHtcbiAgICAgICAgICAgIG1lYXN1cmUoZWxlbWVudCwgb3B0aW9ucy50YXJnZXQsIGluZm8pO1xuICAgICAgICAgICAgdXBkYXRlU2Nyb2xsSW5mbyhlbGVtZW50LCBpbmZvLCB0aW1lKTtcbiAgICAgICAgICAgIGlmIChvcHRpb25zLm9mZnNldCB8fCBvcHRpb25zLnRhcmdldCkge1xuICAgICAgICAgICAgICAgIHJlc29sdmVPZmZzZXRzKGVsZW1lbnQsIGluZm8sIG9wdGlvbnMpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICBub3RpZnk6ICgpID0+IG9uU2Nyb2xsKGluZm8pLFxuICAgIH07XG59XG5cbmV4cG9ydCB7IGNyZWF0ZU9uU2Nyb2xsSGFuZGxlciB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/on-scroll-handler.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs":
/*!************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   scrollInfo: () => (/* binding */ scrollInfo)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/frameloop/frame.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/noop.mjs\");\n/* harmony import */ var _resize_index_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../resize/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/resize/index.mjs\");\n/* harmony import */ var _info_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./info.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/info.mjs\");\n/* harmony import */ var _on_scroll_handler_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./on-scroll-handler.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/on-scroll-handler.mjs\");\n\n\n\n\n\n\nconst scrollListeners = new WeakMap();\nconst resizeListeners = new WeakMap();\nconst onScrollHandlers = new WeakMap();\nconst getEventTarget = (element) => element === document.scrollingElement ? window : element;\nfunction scrollInfo(onScroll, { container = document.scrollingElement, ...options } = {}) {\n    if (!container)\n        return motion_utils__WEBPACK_IMPORTED_MODULE_0__.noop;\n    let containerHandlers = onScrollHandlers.get(container);\n    /**\n     * Get the onScroll handlers for this container.\n     * If one isn't found, create a new one.\n     */\n    if (!containerHandlers) {\n        containerHandlers = new Set();\n        onScrollHandlers.set(container, containerHandlers);\n    }\n    /**\n     * Create a new onScroll handler for the provided callback.\n     */\n    const info = (0,_info_mjs__WEBPACK_IMPORTED_MODULE_1__.createScrollInfo)();\n    const containerHandler = (0,_on_scroll_handler_mjs__WEBPACK_IMPORTED_MODULE_2__.createOnScrollHandler)(container, onScroll, info, options);\n    containerHandlers.add(containerHandler);\n    /**\n     * Check if there's a scroll event listener for this container.\n     * If not, create one.\n     */\n    if (!scrollListeners.has(container)) {\n        const measureAll = () => {\n            for (const handler of containerHandlers) {\n                handler.measure(motion_dom__WEBPACK_IMPORTED_MODULE_3__.frameData.timestamp);\n            }\n            motion_dom__WEBPACK_IMPORTED_MODULE_3__.frame.preUpdate(notifyAll);\n        };\n        const notifyAll = () => {\n            for (const handler of containerHandlers) {\n                handler.notify();\n            }\n        };\n        const listener = () => motion_dom__WEBPACK_IMPORTED_MODULE_3__.frame.read(measureAll);\n        scrollListeners.set(container, listener);\n        const target = getEventTarget(container);\n        window.addEventListener(\"resize\", listener, { passive: true });\n        if (container !== document.documentElement) {\n            resizeListeners.set(container, (0,_resize_index_mjs__WEBPACK_IMPORTED_MODULE_4__.resize)(container, listener));\n        }\n        target.addEventListener(\"scroll\", listener, { passive: true });\n        listener();\n    }\n    const listener = scrollListeners.get(container);\n    motion_dom__WEBPACK_IMPORTED_MODULE_3__.frame.read(listener, false, true);\n    return () => {\n        (0,motion_dom__WEBPACK_IMPORTED_MODULE_3__.cancelFrame)(listener);\n        /**\n         * Check if we even have any handlers for this container.\n         */\n        const currentHandlers = onScrollHandlers.get(container);\n        if (!currentHandlers)\n            return;\n        currentHandlers.delete(containerHandler);\n        if (currentHandlers.size)\n            return;\n        /**\n         * If no more handlers, remove the scroll listener too.\n         */\n        const scrollListener = scrollListeners.get(container);\n        scrollListeners.delete(container);\n        if (scrollListener) {\n            getEventTarget(container).removeEventListener(\"scroll\", scrollListener);\n            resizeListeners.get(container)?.();\n            window.removeEventListener(\"resize\", scrollListener);\n        }\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvcmVuZGVyL2RvbS9zY3JvbGwvdHJhY2subWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUEyRDtBQUN2QjtBQUNTO0FBQ0M7QUFDa0I7O0FBRWhFO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDLG9EQUFvRCxJQUFJO0FBQ3hGO0FBQ0EsZUFBZSw4Q0FBSTtBQUNuQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIsMkRBQWdCO0FBQ2pDLDZCQUE2Qiw2RUFBcUI7QUFDbEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQyxpREFBUztBQUN6QztBQUNBLFlBQVksNkNBQUs7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0JBQStCLDZDQUFLO0FBQ3BDO0FBQ0E7QUFDQSxzREFBc0QsZUFBZTtBQUNyRTtBQUNBLDJDQUEyQyx5REFBTTtBQUNqRDtBQUNBLHNEQUFzRCxlQUFlO0FBQ3JFO0FBQ0E7QUFDQTtBQUNBLElBQUksNkNBQUs7QUFDVDtBQUNBLFFBQVEsdURBQVc7QUFDbkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVzQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxnb2xkX1xccGpzXFxpNS1kMy13YXJyYW50eWFpXFxub2RlX21vZHVsZXNcXGZyYW1lci1tb3Rpb25cXGRpc3RcXGVzXFxyZW5kZXJcXGRvbVxcc2Nyb2xsXFx0cmFjay5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZnJhbWUsIGNhbmNlbEZyYW1lLCBmcmFtZURhdGEgfSBmcm9tICdtb3Rpb24tZG9tJztcbmltcG9ydCB7IG5vb3AgfSBmcm9tICdtb3Rpb24tdXRpbHMnO1xuaW1wb3J0IHsgcmVzaXplIH0gZnJvbSAnLi4vcmVzaXplL2luZGV4Lm1qcyc7XG5pbXBvcnQgeyBjcmVhdGVTY3JvbGxJbmZvIH0gZnJvbSAnLi9pbmZvLm1qcyc7XG5pbXBvcnQgeyBjcmVhdGVPblNjcm9sbEhhbmRsZXIgfSBmcm9tICcuL29uLXNjcm9sbC1oYW5kbGVyLm1qcyc7XG5cbmNvbnN0IHNjcm9sbExpc3RlbmVycyA9IG5ldyBXZWFrTWFwKCk7XG5jb25zdCByZXNpemVMaXN0ZW5lcnMgPSBuZXcgV2Vha01hcCgpO1xuY29uc3Qgb25TY3JvbGxIYW5kbGVycyA9IG5ldyBXZWFrTWFwKCk7XG5jb25zdCBnZXRFdmVudFRhcmdldCA9IChlbGVtZW50KSA9PiBlbGVtZW50ID09PSBkb2N1bWVudC5zY3JvbGxpbmdFbGVtZW50ID8gd2luZG93IDogZWxlbWVudDtcbmZ1bmN0aW9uIHNjcm9sbEluZm8ob25TY3JvbGwsIHsgY29udGFpbmVyID0gZG9jdW1lbnQuc2Nyb2xsaW5nRWxlbWVudCwgLi4ub3B0aW9ucyB9ID0ge30pIHtcbiAgICBpZiAoIWNvbnRhaW5lcilcbiAgICAgICAgcmV0dXJuIG5vb3A7XG4gICAgbGV0IGNvbnRhaW5lckhhbmRsZXJzID0gb25TY3JvbGxIYW5kbGVycy5nZXQoY29udGFpbmVyKTtcbiAgICAvKipcbiAgICAgKiBHZXQgdGhlIG9uU2Nyb2xsIGhhbmRsZXJzIGZvciB0aGlzIGNvbnRhaW5lci5cbiAgICAgKiBJZiBvbmUgaXNuJ3QgZm91bmQsIGNyZWF0ZSBhIG5ldyBvbmUuXG4gICAgICovXG4gICAgaWYgKCFjb250YWluZXJIYW5kbGVycykge1xuICAgICAgICBjb250YWluZXJIYW5kbGVycyA9IG5ldyBTZXQoKTtcbiAgICAgICAgb25TY3JvbGxIYW5kbGVycy5zZXQoY29udGFpbmVyLCBjb250YWluZXJIYW5kbGVycyk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIENyZWF0ZSBhIG5ldyBvblNjcm9sbCBoYW5kbGVyIGZvciB0aGUgcHJvdmlkZWQgY2FsbGJhY2suXG4gICAgICovXG4gICAgY29uc3QgaW5mbyA9IGNyZWF0ZVNjcm9sbEluZm8oKTtcbiAgICBjb25zdCBjb250YWluZXJIYW5kbGVyID0gY3JlYXRlT25TY3JvbGxIYW5kbGVyKGNvbnRhaW5lciwgb25TY3JvbGwsIGluZm8sIG9wdGlvbnMpO1xuICAgIGNvbnRhaW5lckhhbmRsZXJzLmFkZChjb250YWluZXJIYW5kbGVyKTtcbiAgICAvKipcbiAgICAgKiBDaGVjayBpZiB0aGVyZSdzIGEgc2Nyb2xsIGV2ZW50IGxpc3RlbmVyIGZvciB0aGlzIGNvbnRhaW5lci5cbiAgICAgKiBJZiBub3QsIGNyZWF0ZSBvbmUuXG4gICAgICovXG4gICAgaWYgKCFzY3JvbGxMaXN0ZW5lcnMuaGFzKGNvbnRhaW5lcikpIHtcbiAgICAgICAgY29uc3QgbWVhc3VyZUFsbCA9ICgpID0+IHtcbiAgICAgICAgICAgIGZvciAoY29uc3QgaGFuZGxlciBvZiBjb250YWluZXJIYW5kbGVycykge1xuICAgICAgICAgICAgICAgIGhhbmRsZXIubWVhc3VyZShmcmFtZURhdGEudGltZXN0YW1wKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGZyYW1lLnByZVVwZGF0ZShub3RpZnlBbGwpO1xuICAgICAgICB9O1xuICAgICAgICBjb25zdCBub3RpZnlBbGwgPSAoKSA9PiB7XG4gICAgICAgICAgICBmb3IgKGNvbnN0IGhhbmRsZXIgb2YgY29udGFpbmVySGFuZGxlcnMpIHtcbiAgICAgICAgICAgICAgICBoYW5kbGVyLm5vdGlmeSgpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9O1xuICAgICAgICBjb25zdCBsaXN0ZW5lciA9ICgpID0+IGZyYW1lLnJlYWQobWVhc3VyZUFsbCk7XG4gICAgICAgIHNjcm9sbExpc3RlbmVycy5zZXQoY29udGFpbmVyLCBsaXN0ZW5lcik7XG4gICAgICAgIGNvbnN0IHRhcmdldCA9IGdldEV2ZW50VGFyZ2V0KGNvbnRhaW5lcik7XG4gICAgICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKFwicmVzaXplXCIsIGxpc3RlbmVyLCB7IHBhc3NpdmU6IHRydWUgfSk7XG4gICAgICAgIGlmIChjb250YWluZXIgIT09IGRvY3VtZW50LmRvY3VtZW50RWxlbWVudCkge1xuICAgICAgICAgICAgcmVzaXplTGlzdGVuZXJzLnNldChjb250YWluZXIsIHJlc2l6ZShjb250YWluZXIsIGxpc3RlbmVyKSk7XG4gICAgICAgIH1cbiAgICAgICAgdGFyZ2V0LmFkZEV2ZW50TGlzdGVuZXIoXCJzY3JvbGxcIiwgbGlzdGVuZXIsIHsgcGFzc2l2ZTogdHJ1ZSB9KTtcbiAgICAgICAgbGlzdGVuZXIoKTtcbiAgICB9XG4gICAgY29uc3QgbGlzdGVuZXIgPSBzY3JvbGxMaXN0ZW5lcnMuZ2V0KGNvbnRhaW5lcik7XG4gICAgZnJhbWUucmVhZChsaXN0ZW5lciwgZmFsc2UsIHRydWUpO1xuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAgIGNhbmNlbEZyYW1lKGxpc3RlbmVyKTtcbiAgICAgICAgLyoqXG4gICAgICAgICAqIENoZWNrIGlmIHdlIGV2ZW4gaGF2ZSBhbnkgaGFuZGxlcnMgZm9yIHRoaXMgY29udGFpbmVyLlxuICAgICAgICAgKi9cbiAgICAgICAgY29uc3QgY3VycmVudEhhbmRsZXJzID0gb25TY3JvbGxIYW5kbGVycy5nZXQoY29udGFpbmVyKTtcbiAgICAgICAgaWYgKCFjdXJyZW50SGFuZGxlcnMpXG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIGN1cnJlbnRIYW5kbGVycy5kZWxldGUoY29udGFpbmVySGFuZGxlcik7XG4gICAgICAgIGlmIChjdXJyZW50SGFuZGxlcnMuc2l6ZSlcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgLyoqXG4gICAgICAgICAqIElmIG5vIG1vcmUgaGFuZGxlcnMsIHJlbW92ZSB0aGUgc2Nyb2xsIGxpc3RlbmVyIHRvby5cbiAgICAgICAgICovXG4gICAgICAgIGNvbnN0IHNjcm9sbExpc3RlbmVyID0gc2Nyb2xsTGlzdGVuZXJzLmdldChjb250YWluZXIpO1xuICAgICAgICBzY3JvbGxMaXN0ZW5lcnMuZGVsZXRlKGNvbnRhaW5lcik7XG4gICAgICAgIGlmIChzY3JvbGxMaXN0ZW5lcikge1xuICAgICAgICAgICAgZ2V0RXZlbnRUYXJnZXQoY29udGFpbmVyKS5yZW1vdmVFdmVudExpc3RlbmVyKFwic2Nyb2xsXCIsIHNjcm9sbExpc3RlbmVyKTtcbiAgICAgICAgICAgIHJlc2l6ZUxpc3RlbmVycy5nZXQoY29udGFpbmVyKT8uKCk7XG4gICAgICAgICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcihcInJlc2l6ZVwiLCBzY3JvbGxMaXN0ZW5lcik7XG4gICAgICAgIH1cbiAgICB9O1xufVxuXG5leHBvcnQgeyBzY3JvbGxJbmZvIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/utils/get-timeline.mjs":
/*!*************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/scroll/utils/get-timeline.mjs ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getTimeline: () => (/* binding */ getTimeline)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/supports/scroll-timeline.mjs\");\n/* harmony import */ var _track_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../track.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/track.mjs\");\n\n\n\nconst timelineCache = new Map();\nfunction scrollTimelineFallback(options) {\n    const currentTime = { value: 0 };\n    const cancel = (0,_track_mjs__WEBPACK_IMPORTED_MODULE_0__.scrollInfo)((info) => {\n        currentTime.value = info[options.axis].progress * 100;\n    }, options);\n    return { currentTime, cancel };\n}\nfunction getTimeline({ source, container, ...options }) {\n    const { axis } = options;\n    if (source)\n        container = source;\n    const containerCache = timelineCache.get(container) ?? new Map();\n    timelineCache.set(container, containerCache);\n    const targetKey = options.target ?? \"self\";\n    const targetCache = containerCache.get(targetKey) ?? {};\n    const axisKey = axis + (options.offset ?? []).join(\",\");\n    if (!targetCache[axisKey]) {\n        targetCache[axisKey] =\n            !options.target && (0,motion_dom__WEBPACK_IMPORTED_MODULE_1__.supportsScrollTimeline)()\n                ? new ScrollTimeline({ source: container, axis })\n                : scrollTimelineFallback({ container, ...options });\n    }\n    return targetCache[axisKey];\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/utils/get-timeline.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-combine-values.mjs":
/*!*************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-combine-values.mjs ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCombineMotionValues: () => (/* binding */ useCombineMotionValues)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/frameloop/frame.mjs\");\n/* harmony import */ var _utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/use-isomorphic-effect.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs\");\n/* harmony import */ var _use_motion_value_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./use-motion-value.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs\");\n\n\n\n\nfunction useCombineMotionValues(values, combineValues) {\n    /**\n     * Initialise the returned motion value. This remains the same between renders.\n     */\n    const value = (0,_use_motion_value_mjs__WEBPACK_IMPORTED_MODULE_0__.useMotionValue)(combineValues());\n    /**\n     * Create a function that will update the template motion value with the latest values.\n     * This is pre-bound so whenever a motion value updates it can schedule its\n     * execution in Framesync. If it's already been scheduled it won't be fired twice\n     * in a single frame.\n     */\n    const updateValue = () => value.set(combineValues());\n    /**\n     * Synchronously update the motion value with the latest values during the render.\n     * This ensures that within a React render, the styles applied to the DOM are up-to-date.\n     */\n    updateValue();\n    /**\n     * Subscribe to all motion values found within the template. Whenever any of them change,\n     * schedule an update.\n     */\n    (0,_utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_1__.useIsomorphicLayoutEffect)(() => {\n        const scheduleUpdate = () => motion_dom__WEBPACK_IMPORTED_MODULE_2__.frame.preRender(updateValue, false, true);\n        const subscriptions = values.map((v) => v.on(\"change\", scheduleUpdate));\n        return () => {\n            subscriptions.forEach((unsubscribe) => unsubscribe());\n            (0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.cancelFrame)(updateValue);\n        };\n    });\n    return value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-combine-values.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-computed.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-computed.mjs ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useComputed: () => (/* binding */ useComputed)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/value/index.mjs\");\n/* harmony import */ var _use_combine_values_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./use-combine-values.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-combine-values.mjs\");\n\n\n\nfunction useComputed(compute) {\n    /**\n     * Open session of collectMotionValues. Any MotionValue that calls get()\n     * will be saved into this array.\n     */\n    motion_dom__WEBPACK_IMPORTED_MODULE_0__.collectMotionValues.current = [];\n    compute();\n    const value = (0,_use_combine_values_mjs__WEBPACK_IMPORTED_MODULE_1__.useCombineMotionValues)(motion_dom__WEBPACK_IMPORTED_MODULE_0__.collectMotionValues.current, compute);\n    /**\n     * Synchronously close session of collectMotionValues.\n     */\n    motion_dom__WEBPACK_IMPORTED_MODULE_0__.collectMotionValues.current = undefined;\n    return value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdmFsdWUvdXNlLWNvbXB1dGVkLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBaUQ7QUFDaUI7O0FBRWxFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLDJEQUFtQjtBQUN2QjtBQUNBLGtCQUFrQiwrRUFBc0IsQ0FBQywyREFBbUI7QUFDNUQ7QUFDQTtBQUNBO0FBQ0EsSUFBSSwyREFBbUI7QUFDdkI7QUFDQTs7QUFFdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZ29sZF9cXHBqc1xcaTUtZDMtd2FycmFudHlhaVxcbm9kZV9tb2R1bGVzXFxmcmFtZXItbW90aW9uXFxkaXN0XFxlc1xcdmFsdWVcXHVzZS1jb21wdXRlZC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY29sbGVjdE1vdGlvblZhbHVlcyB9IGZyb20gJ21vdGlvbi1kb20nO1xuaW1wb3J0IHsgdXNlQ29tYmluZU1vdGlvblZhbHVlcyB9IGZyb20gJy4vdXNlLWNvbWJpbmUtdmFsdWVzLm1qcyc7XG5cbmZ1bmN0aW9uIHVzZUNvbXB1dGVkKGNvbXB1dGUpIHtcbiAgICAvKipcbiAgICAgKiBPcGVuIHNlc3Npb24gb2YgY29sbGVjdE1vdGlvblZhbHVlcy4gQW55IE1vdGlvblZhbHVlIHRoYXQgY2FsbHMgZ2V0KClcbiAgICAgKiB3aWxsIGJlIHNhdmVkIGludG8gdGhpcyBhcnJheS5cbiAgICAgKi9cbiAgICBjb2xsZWN0TW90aW9uVmFsdWVzLmN1cnJlbnQgPSBbXTtcbiAgICBjb21wdXRlKCk7XG4gICAgY29uc3QgdmFsdWUgPSB1c2VDb21iaW5lTW90aW9uVmFsdWVzKGNvbGxlY3RNb3Rpb25WYWx1ZXMuY3VycmVudCwgY29tcHV0ZSk7XG4gICAgLyoqXG4gICAgICogU3luY2hyb25vdXNseSBjbG9zZSBzZXNzaW9uIG9mIGNvbGxlY3RNb3Rpb25WYWx1ZXMuXG4gICAgICovXG4gICAgY29sbGVjdE1vdGlvblZhbHVlcy5jdXJyZW50ID0gdW5kZWZpbmVkO1xuICAgIHJldHVybiB2YWx1ZTtcbn1cblxuZXhwb3J0IHsgdXNlQ29tcHV0ZWQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-computed.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-motion-value.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMotionValue: () => (/* binding */ useMotionValue)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/value/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../context/MotionConfigContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/use-constant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n\n\n\n\n\n/**\n * Creates a `MotionValue` to track the state and velocity of a value.\n *\n * Usually, these are created automatically. For advanced use-cases, like use with `useTransform`, you can create `MotionValue`s externally and pass them into the animated component via the `style` prop.\n *\n * ```jsx\n * export const MyComponent = () => {\n *   const scale = useMotionValue(1)\n *\n *   return <motion.div style={{ scale }} />\n * }\n * ```\n *\n * @param initial - The initial state.\n *\n * @public\n */\nfunction useMotionValue(initial) {\n    const value = (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_1__.useConstant)(() => (0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.motionValue)(initial));\n    /**\n     * If this motion value is being used in static mode, like on\n     * the Framer canvas, force components to rerender when the motion\n     * value is updated.\n     */\n    const { isStatic } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__.MotionConfigContext);\n    if (isStatic) {\n        const [, setLatest] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initial);\n        (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => value.on(\"change\", setLatest), []);\n    }\n    return value;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-scroll.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useScroll: () => (/* binding */ useScroll)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/value/index.mjs\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/errors.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _render_dom_scroll_index_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../render/dom/scroll/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/scroll/index.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/use-constant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n/* harmony import */ var _utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/use-isomorphic-effect.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs\");\n\n\n\n\n\n\n\nfunction refWarning(name, ref) {\n    (0,motion_utils__WEBPACK_IMPORTED_MODULE_1__.warning)(Boolean(!ref || ref.current), `You have defined a ${name} options but the provided ref is not yet hydrated, probably because it's defined higher up the tree. Try calling useScroll() in the same component as the ref, or setting its \\`layoutEffect: false\\` option.`);\n}\nconst createScrollMotionValues = () => ({\n    scrollX: (0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.motionValue)(0),\n    scrollY: (0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.motionValue)(0),\n    scrollXProgress: (0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.motionValue)(0),\n    scrollYProgress: (0,motion_dom__WEBPACK_IMPORTED_MODULE_2__.motionValue)(0),\n});\nfunction useScroll({ container, target, layoutEffect = true, ...options } = {}) {\n    const values = (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_3__.useConstant)(createScrollMotionValues);\n    const useLifecycleEffect = layoutEffect\n        ? _utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_4__.useIsomorphicLayoutEffect\n        : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n    useLifecycleEffect(() => {\n        refWarning(\"target\", target);\n        refWarning(\"container\", container);\n        return (0,_render_dom_scroll_index_mjs__WEBPACK_IMPORTED_MODULE_5__.scroll)((_progress, { x, y, }) => {\n            values.scrollX.set(x.current);\n            values.scrollXProgress.set(x.progress);\n            values.scrollY.set(y.current);\n            values.scrollYProgress.set(y.progress);\n        }, {\n            ...options,\n            container: container?.current || undefined,\n            target: target?.current || undefined,\n        });\n    }, [container, target, JSON.stringify(options.offset)]);\n    return values;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs":
/*!********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/value/use-transform.mjs ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTransform: () => (/* binding */ useTransform)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/transform.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/use-constant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n/* harmony import */ var _use_combine_values_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./use-combine-values.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-combine-values.mjs\");\n/* harmony import */ var _use_computed_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./use-computed.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-computed.mjs\");\n\n\n\n\n\nfunction useTransform(input, inputRangeOrTransformer, outputRange, options) {\n    if (typeof input === \"function\") {\n        return (0,_use_computed_mjs__WEBPACK_IMPORTED_MODULE_0__.useComputed)(input);\n    }\n    const transformer = typeof inputRangeOrTransformer === \"function\"\n        ? inputRangeOrTransformer\n        : (0,motion_dom__WEBPACK_IMPORTED_MODULE_1__.transform)(inputRangeOrTransformer, outputRange, options);\n    return Array.isArray(input)\n        ? useListTransform(input, transformer)\n        : useListTransform([input], ([latest]) => transformer(latest));\n}\nfunction useListTransform(values, transformer) {\n    const latest = (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_2__.useConstant)(() => []);\n    return (0,_use_combine_values_mjs__WEBPACK_IMPORTED_MODULE_3__.useCombineMotionValues)(values, () => {\n        latest.length = 0;\n        const numValues = values.length;\n        for (let i = 0; i < numValues; i++) {\n            latest[i] = values[i].get();\n        }\n        return transformer(latest);\n    });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/check.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Check)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M20 6 9 17l-5-5\",\n            key: \"1gmf2c\"\n        }\n    ]\n];\nconst Check = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"check\", __iconNode);\n //# sourceMappingURL=check.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2hlY2suanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBR2EsaUJBQXVCO0lBQUM7UUFBQyxNQUFRO1FBQUEsQ0FBRTtZQUFBLEVBQUcsa0JBQW1CO1lBQUEsS0FBSyxDQUFTO1FBQUEsQ0FBQztLQUFDO0NBQUE7QUFhaEYsWUFBUSxrRUFBaUIsVUFBUyxDQUFVIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNyY1xcaWNvbnNcXGNoZWNrLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuaW1wb3J0IHsgSWNvbk5vZGUgfSBmcm9tICcuLi90eXBlcyc7XG5cbmV4cG9ydCBjb25zdCBfX2ljb25Ob2RlOiBJY29uTm9kZSA9IFtbJ3BhdGgnLCB7IGQ6ICdNMjAgNiA5IDE3bC01LTUnLCBrZXk6ICcxZ21mMmMnIH1dXTtcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIENoZWNrXG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThjR0YwYUNCa1BTSk5NakFnTmlBNUlERTNiQzAxTFRVaUlDOCtDand2YzNablBnbz0pIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL2NoZWNrXG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgQ2hlY2sgPSBjcmVhdGVMdWNpZGVJY29uKCdjaGVjaycsIF9faWNvbk5vZGUpO1xuXG5leHBvcnQgZGVmYXVsdCBDaGVjaztcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cloud.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/cloud.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Cloud)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z\",\n            key: \"p7xjir\"\n        }\n    ]\n];\nconst Cloud = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"cloud\", __iconNode);\n //# sourceMappingURL=cloud.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2xvdWQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBR08sTUFBTSxVQUF1QjtJQUNsQztRQUFDLE1BQVE7UUFBQTtZQUFFLEdBQUcsQ0FBdUQ7WUFBQSxLQUFLO1FBQUEsQ0FBVTtLQUFBO0NBQ3RGO0FBYU0sWUFBUSxrRUFBaUIsVUFBUyxDQUFVIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNyY1xcaWNvbnNcXGNsb3VkLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuaW1wb3J0IHsgSWNvbk5vZGUgfSBmcm9tICcuLi90eXBlcyc7XG5cbmV4cG9ydCBjb25zdCBfX2ljb25Ob2RlOiBJY29uTm9kZSA9IFtcbiAgWydwYXRoJywgeyBkOiAnTTE3LjUgMTlIOWE3IDcgMCAxIDEgNi43MS05aDEuNzlhNC41IDQuNSAwIDEgMSAwIDlaJywga2V5OiAncDd4amlyJyB9XSxcbl07XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBDbG91ZFxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4Y0dGMGFDQmtQU0pOTVRjdU5TQXhPVWc1WVRjZ055QXdJREVnTVNBMkxqY3hMVGxvTVM0M09XRTBMalVnTkM0MUlEQWdNU0F4SURBZ09Wb2lJQzgrQ2p3dmMzWm5QZ289KSAtIGh0dHBzOi8vbHVjaWRlLmRldi9pY29ucy9jbG91ZFxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IENsb3VkID0gY3JlYXRlTHVjaWRlSWNvbignY2xvdWQnLCBfX2ljb25Ob2RlKTtcblxuZXhwb3J0IGRlZmF1bHQgQ2xvdWQ7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cloud.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/crown.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Crown)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z\",\n            key: \"1vdc57\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M5 21h14\",\n            key: \"11awu3\"\n        }\n    ]\n];\nconst Crown = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"crown\", __iconNode);\n //# sourceMappingURL=crown.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js":
/*!***************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/file-text.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ FileText)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z\",\n            key: \"1rqfz7\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n            key: \"tnqrlb\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M10 9H8\",\n            key: \"b1mrlr\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M16 13H8\",\n            key: \"t4e002\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M16 17H8\",\n            key: \"z1uh3a\"\n        }\n    ]\n];\nconst FileText = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"file-text\", __iconNode);\n //# sourceMappingURL=file-text.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/github.js":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/github.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Github)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4\",\n            key: \"tonef\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M9 18c-4.51 2-5-2-7-2\",\n            key: \"9comsn\"\n        }\n    ]\n];\nconst Github = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"github\", __iconNode);\n //# sourceMappingURL=github.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/github.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/heart.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Heart)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z\",\n            key: \"c3ymky\"\n        }\n    ]\n];\nconst Heart = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"heart\", __iconNode);\n //# sourceMappingURL=heart.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvaGVhcnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBR08sTUFBTSxVQUF1QjtJQUNsQztRQUNFO1FBQ0E7WUFDRSxDQUFHO1lBQ0gsR0FBSztRQUNQO0tBQ0Y7Q0FDRjtBQWFNLFlBQVEsa0VBQWlCLFVBQVMsQ0FBVSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzcmNcXGljb25zXFxoZWFydC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcbmltcG9ydCB7IEljb25Ob2RlIH0gZnJvbSAnLi4vdHlwZXMnO1xuXG5leHBvcnQgY29uc3QgX19pY29uTm9kZTogSWNvbk5vZGUgPSBbXG4gIFtcbiAgICAncGF0aCcsXG4gICAge1xuICAgICAgZDogJ00xOSAxNGMxLjQ5LTEuNDYgMy0zLjIxIDMtNS41QTUuNSA1LjUgMCAwIDAgMTYuNSAzYy0xLjc2IDAtMyAuNS00LjUgMi0xLjUtMS41LTIuNzQtMi00LjUtMkE1LjUgNS41IDAgMCAwIDIgOC41YzAgMi4zIDEuNSA0LjA1IDMgNS41bDcgN1onLFxuICAgICAga2V5OiAnYzN5bWt5JyxcbiAgICB9LFxuICBdLFxuXTtcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIEhlYXJ0XG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThjR0YwYUNCa1BTSk5NVGtnTVRSak1TNDBPUzB4TGpRMklETXRNeTR5TVNBekxUVXVOVUUxTGpVZ05TNDFJREFnTUNBd0lERTJMalVnTTJNdE1TNDNOaUF3TFRNZ0xqVXROQzQxSURJdE1TNDFMVEV1TlMweUxqYzBMVEl0TkM0MUxUSkJOUzQxSURVdU5TQXdJREFnTUNBeUlEZ3VOV013SURJdU15QXhMalVnTkM0d05TQXpJRFV1Tld3M0lEZGFJaUF2UGdvOEwzTjJaejRLKSAtIGh0dHBzOi8vbHVjaWRlLmRldi9pY29ucy9oZWFydFxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IEhlYXJ0ID0gY3JlYXRlTHVjaWRlSWNvbignaGVhcnQnLCBfX2ljb25Ob2RlKTtcblxuZXhwb3J0IGRlZmF1bHQgSGVhcnQ7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/infinity.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/infinity.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Infinity)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M6 16c5 0 7-8 12-8a4 4 0 0 1 0 8c-5 0-7-8-12-8a4 4 0 1 0 0 8\",\n            key: \"18ogeb\"\n        }\n    ]\n];\nconst Infinity = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"infinity\", __iconNode);\n //# sourceMappingURL=infinity.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvaW5maW5pdHkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBR08sTUFBTSxVQUF1QjtJQUNsQztRQUFDLE1BQVE7UUFBQTtZQUFFLEdBQUcsQ0FBZ0U7WUFBQSxLQUFLO1FBQUEsQ0FBVTtLQUFBO0NBQy9GO0FBYU0sZUFBVyxrRUFBaUIsYUFBWSxDQUFVIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNyY1xcaWNvbnNcXGluZmluaXR5LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuaW1wb3J0IHsgSWNvbk5vZGUgfSBmcm9tICcuLi90eXBlcyc7XG5cbmV4cG9ydCBjb25zdCBfX2ljb25Ob2RlOiBJY29uTm9kZSA9IFtcbiAgWydwYXRoJywgeyBkOiAnTTYgMTZjNSAwIDctOCAxMi04YTQgNCAwIDAgMSAwIDhjLTUgMC03LTgtMTItOGE0IDQgMCAxIDAgMCA4Jywga2V5OiAnMThvZ2ViJyB9XSxcbl07XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBJbmZpbml0eVxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4Y0dGMGFDQmtQU0pOTmlBeE5tTTFJREFnTnkwNElERXlMVGhoTkNBMElEQWdNQ0F4SURBZ09HTXROU0F3TFRjdE9DMHhNaTA0WVRRZ05DQXdJREVnTUNBd0lEZ2lJQzgrQ2p3dmMzWm5QZ289KSAtIGh0dHBzOi8vbHVjaWRlLmRldi9pY29ucy9pbmZpbml0eVxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IEluZmluaXR5ID0gY3JlYXRlTHVjaWRlSWNvbignaW5maW5pdHknLCBfX2ljb25Ob2RlKTtcblxuZXhwb3J0IGRlZmF1bHQgSW5maW5pdHk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/infinity.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/linkedin.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/linkedin.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Linkedin)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z\",\n            key: \"c2jq9f\"\n        }\n    ],\n    [\n        \"rect\",\n        {\n            width: \"4\",\n            height: \"12\",\n            x: \"2\",\n            y: \"9\",\n            key: \"mk3on5\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"4\",\n            cy: \"4\",\n            r: \"2\",\n            key: \"bt5ra8\"\n        }\n    ]\n];\nconst Linkedin = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"linkedin\", __iconNode);\n //# sourceMappingURL=linkedin.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/mail.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Mail)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7\",\n            key: \"132q7q\"\n        }\n    ],\n    [\n        \"rect\",\n        {\n            x: \"2\",\n            y: \"4\",\n            width: \"20\",\n            height: \"16\",\n            rx: \"2\",\n            key: \"izxlao\"\n        }\n    ]\n];\nconst Mail = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"mail\", __iconNode);\n //# sourceMappingURL=mail.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/rocket.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Rocket)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z\",\n            key: \"m3kijz\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z\",\n            key: \"1fmvmk\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0\",\n            key: \"1f8sc4\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5\",\n            key: \"qeys4\"\n        }\n    ]\n];\nconst Rocket = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"rocket\", __iconNode);\n //# sourceMappingURL=rocket.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/star.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Star)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z\",\n            key: \"r04s7s\"\n        }\n    ]\n];\nconst Star = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"star\", __iconNode);\n //# sourceMappingURL=star.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/twitter.js":
/*!*************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/twitter.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Twitter)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z\",\n            key: \"pff0z6\"\n        }\n    ]\n];\nconst Twitter = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"twitter\", __iconNode);\n //# sourceMappingURL=twitter.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvdHdpdHRlci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFHTyxNQUFNLFVBQXVCO0lBQ2xDO1FBQ0U7UUFDQTtZQUNFLENBQUc7WUFDSCxHQUFLO1FBQ1A7S0FDRjtDQUNGO0FBYU0sY0FBVSxrRUFBaUIsWUFBVyxDQUFVIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNyY1xcaWNvbnNcXHR3aXR0ZXIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbic7XG5pbXBvcnQgeyBJY29uTm9kZSB9IGZyb20gJy4uL3R5cGVzJztcblxuZXhwb3J0IGNvbnN0IF9faWNvbk5vZGU6IEljb25Ob2RlID0gW1xuICBbXG4gICAgJ3BhdGgnLFxuICAgIHtcbiAgICAgIGQ6ICdNMjIgNHMtLjcgMi4xLTIgMy40YzEuNiAxMC05LjQgMTcuMy0xOCAxMS42IDIuMi4xIDQuNC0uNiA2LTJDMyAxNS41LjUgOS42IDMgNWMyLjIgMi42IDUuNiA0LjEgOSA0LS45LTQuMiA0LTYuNiA3LTMuOCAxLjEgMCAzLTEuMiAzLTEuMnonLFxuICAgICAga2V5OiAncGZmMHo2JyxcbiAgICB9LFxuICBdLFxuXTtcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIFR3aXR0ZXJcbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNHRjBhQ0JrUFNKTk1qSWdOSE10TGpjZ01pNHhMVElnTXk0MFl6RXVOaUF4TUMwNUxqUWdNVGN1TXkweE9DQXhNUzQySURJdU1pNHhJRFF1TkMwdU5pQTJMVEpETXlBeE5TNDFMalVnT1M0MklETWdOV015TGpJZ01pNDJJRFV1TmlBMExqRWdPU0EwTFM0NUxUUXVNaUEwTFRZdU5pQTNMVE11T0NBeExqRWdNQ0F6TFRFdU1pQXpMVEV1TW5vaUlDOCtDand2YzNablBnbz0pIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL3R3aXR0ZXJcbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS1yZWFjdCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7SlNYLkVsZW1lbnR9IEpTWCBFbGVtZW50XG4gKiBAZGVwcmVjYXRlZCBCcmFuZCBpY29ucyBoYXZlIGJlZW4gZGVwcmVjYXRlZCBhbmQgYXJlIGR1ZSB0byBiZSByZW1vdmVkLCBwbGVhc2UgcmVmZXIgdG8gaHR0cHM6Ly9naXRodWIuY29tL2x1Y2lkZS1pY29ucy9sdWNpZGUvaXNzdWVzLzY3MC4gV2UgcmVjb21tZW5kIHVzaW5nIGh0dHBzOi8vc2ltcGxlaWNvbnMub3JnLz9xPXR3aXR0ZXIgaW5zdGVhZC4gVGhpcyBpY29uIHdpbGwgYmUgcmVtb3ZlZCBpbiB2MS4wXG4gKi9cbmNvbnN0IFR3aXR0ZXIgPSBjcmVhdGVMdWNpZGVJY29uKCd0d2l0dGVyJywgX19pY29uTm9kZSk7XG5cbmV4cG9ydCBkZWZhdWx0IFR3aXR0ZXI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/twitter.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/scroll/observe.mjs":
/*!************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/scroll/observe.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   observeTimeline: () => (/* binding */ observeTimeline)\n/* harmony export */ });\n/* harmony import */ var _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../frameloop/frame.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/frameloop/frame.mjs\");\n\n\nfunction observeTimeline(update, timeline) {\n    let prevProgress;\n    const onFrame = () => {\n        const { currentTime } = timeline;\n        const percentage = currentTime === null ? 0 : currentTime.value;\n        const progress = percentage / 100;\n        if (prevProgress !== progress) {\n            update(progress);\n        }\n        prevProgress = progress;\n    };\n    _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__.frame.preUpdate(onFrame, true);\n    return () => (0,_frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__.cancelFrame)(onFrame);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvc2Nyb2xsL29ic2VydmUubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTREOztBQUU1RDtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsY0FBYztBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUksdURBQUs7QUFDVCxpQkFBaUIsaUVBQVc7QUFDNUI7O0FBRTJCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGdvbGRfXFxwanNcXGk1LWQzLXdhcnJhbnR5YWlcXG5vZGVfbW9kdWxlc1xcbW90aW9uLWRvbVxcZGlzdFxcZXNcXHNjcm9sbFxcb2JzZXJ2ZS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZnJhbWUsIGNhbmNlbEZyYW1lIH0gZnJvbSAnLi4vZnJhbWVsb29wL2ZyYW1lLm1qcyc7XG5cbmZ1bmN0aW9uIG9ic2VydmVUaW1lbGluZSh1cGRhdGUsIHRpbWVsaW5lKSB7XG4gICAgbGV0IHByZXZQcm9ncmVzcztcbiAgICBjb25zdCBvbkZyYW1lID0gKCkgPT4ge1xuICAgICAgICBjb25zdCB7IGN1cnJlbnRUaW1lIH0gPSB0aW1lbGluZTtcbiAgICAgICAgY29uc3QgcGVyY2VudGFnZSA9IGN1cnJlbnRUaW1lID09PSBudWxsID8gMCA6IGN1cnJlbnRUaW1lLnZhbHVlO1xuICAgICAgICBjb25zdCBwcm9ncmVzcyA9IHBlcmNlbnRhZ2UgLyAxMDA7XG4gICAgICAgIGlmIChwcmV2UHJvZ3Jlc3MgIT09IHByb2dyZXNzKSB7XG4gICAgICAgICAgICB1cGRhdGUocHJvZ3Jlc3MpO1xuICAgICAgICB9XG4gICAgICAgIHByZXZQcm9ncmVzcyA9IHByb2dyZXNzO1xuICAgIH07XG4gICAgZnJhbWUucHJlVXBkYXRlKG9uRnJhbWUsIHRydWUpO1xuICAgIHJldHVybiAoKSA9PiBjYW5jZWxGcmFtZShvbkZyYW1lKTtcbn1cblxuZXhwb3J0IHsgb2JzZXJ2ZVRpbWVsaW5lIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/scroll/observe.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/transform.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/motion-dom/dist/es/utils/transform.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   transform: () => (/* binding */ transform)\n/* harmony export */ });\n/* harmony import */ var _interpolate_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./interpolate.mjs */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/interpolate.mjs\");\n\n\nfunction transform(...args) {\n    const useImmediate = !Array.isArray(args[0]);\n    const argOffset = useImmediate ? 0 : -1;\n    const inputValue = args[0 + argOffset];\n    const inputRange = args[1 + argOffset];\n    const outputRange = args[2 + argOffset];\n    const options = args[3 + argOffset];\n    const interpolator = (0,_interpolate_mjs__WEBPACK_IMPORTED_MODULE_0__.interpolate)(inputRange, outputRange, options);\n    return useImmediate ? interpolator(inputValue) : interpolator;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9tb3Rpb24tZG9tL2Rpc3QvZXMvdXRpbHMvdHJhbnNmb3JtLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFnRDs7QUFFaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIsNkRBQVc7QUFDcEM7QUFDQTs7QUFFcUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZ29sZF9cXHBqc1xcaTUtZDMtd2FycmFudHlhaVxcbm9kZV9tb2R1bGVzXFxtb3Rpb24tZG9tXFxkaXN0XFxlc1xcdXRpbHNcXHRyYW5zZm9ybS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaW50ZXJwb2xhdGUgfSBmcm9tICcuL2ludGVycG9sYXRlLm1qcyc7XG5cbmZ1bmN0aW9uIHRyYW5zZm9ybSguLi5hcmdzKSB7XG4gICAgY29uc3QgdXNlSW1tZWRpYXRlID0gIUFycmF5LmlzQXJyYXkoYXJnc1swXSk7XG4gICAgY29uc3QgYXJnT2Zmc2V0ID0gdXNlSW1tZWRpYXRlID8gMCA6IC0xO1xuICAgIGNvbnN0IGlucHV0VmFsdWUgPSBhcmdzWzAgKyBhcmdPZmZzZXRdO1xuICAgIGNvbnN0IGlucHV0UmFuZ2UgPSBhcmdzWzEgKyBhcmdPZmZzZXRdO1xuICAgIGNvbnN0IG91dHB1dFJhbmdlID0gYXJnc1syICsgYXJnT2Zmc2V0XTtcbiAgICBjb25zdCBvcHRpb25zID0gYXJnc1szICsgYXJnT2Zmc2V0XTtcbiAgICBjb25zdCBpbnRlcnBvbGF0b3IgPSBpbnRlcnBvbGF0ZShpbnB1dFJhbmdlLCBvdXRwdXRSYW5nZSwgb3B0aW9ucyk7XG4gICAgcmV0dXJuIHVzZUltbWVkaWF0ZSA/IGludGVycG9sYXRvcihpbnB1dFZhbHVlKSA6IGludGVycG9sYXRvcjtcbn1cblxuZXhwb3J0IHsgdHJhbnNmb3JtIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/motion-dom/dist/es/utils/transform.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgold_%5C%5Cpjs%5C%5Ci5-d3-warrantyai%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CFeaturePreviewSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgold_%5C%5Cpjs%5C%5Ci5-d3-warrantyai%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgold_%5C%5Cpjs%5C%5Ci5-d3-warrantyai%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CHeroSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgold_%5C%5Cpjs%5C%5Ci5-d3-warrantyai%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CPricingSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgold_%5C%5Cpjs%5C%5Ci5-d3-warrantyai%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CProblemSolutionSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgold_%5C%5Cpjs%5C%5Ci5-d3-warrantyai%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CProcessSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgold_%5C%5Cpjs%5C%5Ci5-d3-warrantyai%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CFeaturePreviewSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgold_%5C%5Cpjs%5C%5Ci5-d3-warrantyai%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgold_%5C%5Cpjs%5C%5Ci5-d3-warrantyai%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CHeroSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgold_%5C%5Cpjs%5C%5Ci5-d3-warrantyai%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CPricingSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgold_%5C%5Cpjs%5C%5Ci5-d3-warrantyai%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CProblemSolutionSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgold_%5C%5Cpjs%5C%5Ci5-d3-warrantyai%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CProcessSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/FeaturePreviewSection.tsx */ \"(app-pages-browser)/./src/components/sections/FeaturePreviewSection.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/Footer.tsx */ \"(app-pages-browser)/./src/components/sections/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/HeroSection.tsx */ \"(app-pages-browser)/./src/components/sections/HeroSection.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/PricingSection.tsx */ \"(app-pages-browser)/./src/components/sections/PricingSection.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/ProblemSolutionSection.tsx */ \"(app-pages-browser)/./src/components/sections/ProblemSolutionSection.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/ProcessSection.tsx */ \"(app-pages-browser)/./src/components/sections/ProcessSection.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgold_%5C%5Cpjs%5C%5Ci5-d3-warrantyai%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CFeaturePreviewSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgold_%5C%5Cpjs%5C%5Ci5-d3-warrantyai%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgold_%5C%5Cpjs%5C%5Ci5-d3-warrantyai%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CHeroSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgold_%5C%5Cpjs%5C%5Ci5-d3-warrantyai%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CPricingSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgold_%5C%5Cpjs%5C%5Ci5-d3-warrantyai%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CProblemSolutionSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cgold_%5C%5Cpjs%5C%5Ci5-d3-warrantyai%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CProcessSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/sections/FeaturePreviewSection.tsx":
/*!***********************************************************!*\
  !*** ./src/components/sections/FeaturePreviewSection.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Bell_Brain_Camera_Cloud_FileText_Search_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Brain,Camera,Cloud,FileText,Search,Shield,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Brain_Camera_Cloud_FileText_Search_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Brain,Camera,Cloud,FileText,Search,Shield,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Brain_Camera_Cloud_FileText_Search_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Brain,Camera,Cloud,FileText,Search,Shield,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Brain_Camera_Cloud_FileText_Search_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Brain,Camera,Cloud,FileText,Search,Shield,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Brain_Camera_Cloud_FileText_Search_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Brain,Camera,Cloud,FileText,Search,Shield,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Brain_Camera_Cloud_FileText_Search_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Brain,Camera,Cloud,FileText,Search,Shield,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cloud.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Brain_Camera_Cloud_FileText_Search_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Brain,Camera,Cloud,FileText,Search,Shield,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Brain_Camera_Cloud_FileText_Search_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Brain,Camera,Cloud,FileText,Search,Shield,Smartphone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst features = [\n    {\n        id: 1,\n        title: \"AI-Powered Recognition\",\n        description: \"Advanced computer vision instantly extracts warranty information from any receipt, photo, or document.\",\n        icon: _barrel_optimize_names_Bell_Brain_Camera_Cloud_FileText_Search_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        color: \"cyan-400\",\n        stats: \"99% accuracy\",\n        demo: \"receipt-scan\"\n    },\n    {\n        id: 2,\n        title: \"Smart Notifications\",\n        description: \"Intelligent reminders ensure you never miss warranty expiry dates or important service windows.\",\n        icon: _barrel_optimize_names_Bell_Brain_Camera_Cloud_FileText_Search_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        color: \"purple-400\",\n        stats: \"24/7 monitoring\",\n        demo: \"notifications\"\n    },\n    {\n        id: 3,\n        title: \"Secure Cloud Storage\",\n        description: \"Military-grade encryption keeps all your warranty documents safe and accessible from anywhere.\",\n        icon: _barrel_optimize_names_Bell_Brain_Camera_Cloud_FileText_Search_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        color: \"green-400\",\n        stats: \"Bank-level security\",\n        demo: \"cloud-storage\"\n    },\n    {\n        id: 4,\n        title: \"Universal Compatibility\",\n        description: \"Works with any device, any receipt format, and integrates with your existing email and shopping accounts.\",\n        icon: _barrel_optimize_names_Bell_Brain_Camera_Cloud_FileText_Search_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        color: \"blue-400\",\n        stats: \"All platforms\",\n        demo: \"compatibility\"\n    }\n];\nconst FeaturePreviewSection = ()=>{\n    _s();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [activeFeature, setActiveFeature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const { scrollYProgress } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_7__.useScroll)({\n        target: containerRef,\n        offset: [\n            \"start end\",\n            \"end start\"\n        ]\n    });\n    // Parallax transforms for different layers\n    const backgroundY = (0,framer_motion__WEBPACK_IMPORTED_MODULE_8__.useTransform)(scrollYProgress, [\n        0,\n        1\n    ], [\n        \"0%\",\n        \"50%\"\n    ]);\n    const layer1Y = (0,framer_motion__WEBPACK_IMPORTED_MODULE_8__.useTransform)(scrollYProgress, [\n        0,\n        1\n    ], [\n        \"0%\",\n        \"25%\"\n    ]);\n    const layer2Y = (0,framer_motion__WEBPACK_IMPORTED_MODULE_8__.useTransform)(scrollYProgress, [\n        0,\n        1\n    ], [\n        \"0%\",\n        \"15%\"\n    ]);\n    const layer3Y = (0,framer_motion__WEBPACK_IMPORTED_MODULE_8__.useTransform)(scrollYProgress, [\n        0,\n        1\n    ], [\n        \"0%\",\n        \"10%\"\n    ]);\n    // Auto-cycle through features\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FeaturePreviewSection.useEffect\": ()=>{\n            const interval = setInterval({\n                \"FeaturePreviewSection.useEffect.interval\": ()=>{\n                    setActiveFeature({\n                        \"FeaturePreviewSection.useEffect.interval\": (prev)=>(prev + 1) % features.length\n                    }[\"FeaturePreviewSection.useEffect.interval\"]);\n                }\n            }[\"FeaturePreviewSection.useEffect.interval\"], 4000);\n            return ({\n                \"FeaturePreviewSection.useEffect\": ()=>clearInterval(interval)\n            })[\"FeaturePreviewSection.useEffect\"];\n        }\n    }[\"FeaturePreviewSection.useEffect\"], []);\n    const currentFeature = features[activeFeature];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: containerRef,\n        className: \"relative py-24 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        style: {\n                            y: backgroundY\n                        },\n                        className: \"absolute inset-0 bg-gradient-to-br from-dark-950 via-dark-900 to-dark-950\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        style: {\n                            y: layer1Y\n                        },\n                        className: \"absolute inset-0 opacity-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mesh-gradient h-full w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        style: {\n                            y: layer2Y\n                        },\n                        className: \"absolute inset-0 opacity-30\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/4 left-1/4 w-64 h-64 border border-cyan-400/20 rounded-full animate-spin-slow\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-1/4 right-1/4 w-48 h-48 border border-purple-400/20 rounded-lg animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-3/4 left-3/4 w-32 h-32 border border-green-400/20 rounded-full animate-ping-slow\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        style: {\n                            y: layer3Y\n                        },\n                        className: \"absolute inset-0\",\n                        children: [\n                            ...Array(30)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                className: \"absolute w-1 h-1 bg-white/30 rounded-full\",\n                                style: {\n                                    left: \"\".concat(Math.random() * 100, \"%\"),\n                                    top: \"\".concat(Math.random() * 100, \"%\")\n                                },\n                                animate: {\n                                    y: [\n                                        0,\n                                        -30,\n                                        0\n                                    ],\n                                    opacity: [\n                                        0.3,\n                                        1,\n                                        0.3\n                                    ],\n                                    scale: [\n                                        1,\n                                        1.5,\n                                        1\n                                    ]\n                                },\n                                transition: {\n                                    duration: 4 + Math.random() * 2,\n                                    repeat: Infinity,\n                                    delay: Math.random() * 2\n                                }\n                            }, i, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        style: {\n                            y: layer1Y\n                        },\n                        className: \"absolute inset-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/3 left-1/2 w-96 h-96 bg-cyan-400/10 rounded-full blur-3xl animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-1/3 right-1/2 w-80 h-80 bg-purple-400/10 rounded-full blur-3xl animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.h2, {\n                                className: \"text-4xl lg:text-6xl font-display font-bold mb-6\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"gradient-text\",\n                                    children: \"Powerful Features\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.p, {\n                                className: \"text-xl text-gray-300 max-w-3xl mx-auto\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.2\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: \"Experience the future of warranty management with cutting-edge AI and intuitive design.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid lg:grid-cols-2 gap-12 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: -50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"space-y-6\",\n                                children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative cursor-pointer transition-all duration-500\", activeFeature === index ? \"scale-105\" : \"hover:scale-102\"),\n                                        onClick: ()=>setActiveFeature(index),\n                                        whileHover: {\n                                            x: 10\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"glass-card p-6 border-l-4 transition-all duration-500\", activeFeature === index ? \"border-\".concat(feature.color, \" bg-white/10 shadow-2xl\") : \"border-gray-600 hover:border-gray-400\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"w-12 h-12 rounded-full flex items-center justify-center transition-all duration-500\", activeFeature === index ? \"bg-\".concat(feature.color, \"/20 border-2 border-\").concat(feature.color, \" shadow-lg\") : \"bg-gray-700 border-2 border-gray-600\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"w-6 h-6 transition-colors duration-500\", activeFeature === index ? \"text-\".concat(feature.color) : \"text-gray-400\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-xl font-bold transition-colors duration-500\", activeFeature === index ? \"text-white\" : \"text-gray-300\"),\n                                                                        children: feature.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                                                        lineNumber: 215,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-xs font-medium px-2 py-1 rounded-full transition-all duration-500\", activeFeature === index ? \"bg-\".concat(feature.color, \"/20 text-\").concat(feature.color) : \"bg-gray-700 text-gray-400\"),\n                                                                        children: feature.stats\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                                                        lineNumber: 222,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                                                lineNumber: 214,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-gray-400 transition-colors duration-500\", activeFeature === index && \"text-gray-300\"),\n                                                                children: feature.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                                                lineNumber: 232,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            activeFeature === index && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                                initial: {\n                                                                    width: 0\n                                                                },\n                                                                animate: {\n                                                                    width: \"100%\"\n                                                                },\n                                                                transition: {\n                                                                    duration: 4\n                                                                },\n                                                                className: \"h-1 bg-\".concat(feature.color, \" rounded-full mt-3\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                                                lineNumber: 241,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, feature.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    x: 50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    x: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.2\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"relative\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"glass-card p-8 h-96 flex flex-col justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.AnimatePresence, {\n                                        mode: \"wait\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                scale: 0.8,\n                                                rotateY: 90\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                scale: 1,\n                                                rotateY: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                scale: 0.8,\n                                                rotateY: -90\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                ease: \"easeInOut\"\n                                            },\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                    className: \"w-20 h-20 mx-auto bg-\".concat(currentFeature.color, \"/20 border-2 border-\").concat(currentFeature.color, \" rounded-full flex items-center justify-center mb-6\"),\n                                                    animate: {\n                                                        scale: [\n                                                            1,\n                                                            1.1,\n                                                            1\n                                                        ],\n                                                        rotate: currentFeature.demo === 'receipt-scan' ? [\n                                                            0,\n                                                            360\n                                                        ] : [\n                                                            0,\n                                                            5,\n                                                            -5,\n                                                            0\n                                                        ]\n                                                    },\n                                                    transition: {\n                                                        duration: currentFeature.demo === 'receipt-scan' ? 3 : 2,\n                                                        repeat: Infinity,\n                                                        ease: \"easeInOut\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(currentFeature.icon, {\n                                                        className: \"w-8 h-8 text-\".concat(currentFeature.color)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl font-bold mb-4 text-white\",\n                                                    children: currentFeature.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-300 mb-6\",\n                                                    children: currentFeature.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        currentFeature.demo === 'receipt-scan' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-gray-800 rounded-lg p-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Brain_Camera_Cloud_FileText_Search_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"w-4 h-4 text-cyan-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                                                            lineNumber: 302,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-cyan-400\",\n                                                                            children: \"Scanning Receipt...\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                                                            lineNumber: 303,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                                                    lineNumber: 301,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full bg-gray-700 rounded-full h-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                                        className: \"bg-cyan-400 h-2 rounded-full\",\n                                                                        initial: {\n                                                                            width: 0\n                                                                        },\n                                                                        animate: {\n                                                                            width: \"100%\"\n                                                                        },\n                                                                        transition: {\n                                                                            duration: 2,\n                                                                            repeat: Infinity\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                                                        lineNumber: 306,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                                                    lineNumber: 305,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        currentFeature.demo === 'notifications' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                'Warranty expires in 30 days',\n                                                                'Service reminder due',\n                                                                'New warranty added'\n                                                            ].map((notif, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                                    initial: {\n                                                                        opacity: 0,\n                                                                        x: -20\n                                                                    },\n                                                                    animate: {\n                                                                        opacity: 1,\n                                                                        x: 0\n                                                                    },\n                                                                    transition: {\n                                                                        delay: idx * 0.5\n                                                                    },\n                                                                    className: \"bg-purple-400/10 border border-purple-400/30 rounded-lg p-2 text-left\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Brain_Camera_Cloud_FileText_Search_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                className: \"w-3 h-3 text-purple-400\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                                                                lineNumber: 327,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs text-gray-300\",\n                                                                                children: notif\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                                                                lineNumber: 328,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                                                        lineNumber: 326,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, notif, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                                                    lineNumber: 319,\n                                                                    columnNumber: 27\n                                                                }, undefined))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        currentFeature.demo === 'cloud-storage' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-gray-800 rounded-lg p-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-400\",\n                                                                            children: \"Storage Used\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                                                            lineNumber: 338,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-green-400\",\n                                                                            children: \"2.4GB / 100GB\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                                                            lineNumber: 339,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                                                    lineNumber: 337,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full bg-gray-700 rounded-full h-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"bg-green-400 h-2 rounded-full w-1/12\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                                                        lineNumber: 342,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                                                    lineNumber: 341,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 mt-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Brain_Camera_Cloud_FileText_Search_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"w-4 h-4 text-green-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                                                            lineNumber: 345,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-green-400\",\n                                                                            children: \"Synced & Secure\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                                                            lineNumber: 346,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                                                    lineNumber: 344,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        currentFeature.demo === 'compatibility' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-3 gap-2\",\n                                                            children: [\n                                                                _barrel_optimize_names_Bell_Brain_Camera_Cloud_FileText_Search_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                                                                _barrel_optimize_names_Bell_Brain_Camera_Cloud_FileText_Search_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                                                                _barrel_optimize_names_Bell_Brain_Camera_Cloud_FileText_Search_Shield_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n                                                            ].map((Icon, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                                    initial: {\n                                                                        opacity: 0,\n                                                                        scale: 0\n                                                                    },\n                                                                    animate: {\n                                                                        opacity: 1,\n                                                                        scale: 1\n                                                                    },\n                                                                    transition: {\n                                                                        delay: idx * 0.2\n                                                                    },\n                                                                    className: \"bg-blue-400/10 border border-blue-400/30 rounded-lg p-3 flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                        className: \"w-6 h-6 text-blue-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                                                        lineNumber: 361,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, idx, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                                                    lineNumber: 354,\n                                                                    columnNumber: 27\n                                                                }, undefined))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                                            lineNumber: 352,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, activeFeature, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\FeaturePreviewSection.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FeaturePreviewSection, \"jox3r4ct+vYt0Iy3GWHgt4HJGNs=\", false, function() {\n    return [\n        framer_motion__WEBPACK_IMPORTED_MODULE_7__.useScroll,\n        framer_motion__WEBPACK_IMPORTED_MODULE_8__.useTransform,\n        framer_motion__WEBPACK_IMPORTED_MODULE_8__.useTransform,\n        framer_motion__WEBPACK_IMPORTED_MODULE_8__.useTransform,\n        framer_motion__WEBPACK_IMPORTED_MODULE_8__.useTransform\n    ];\n});\n_c = FeaturePreviewSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FeaturePreviewSection);\nvar _c;\n$RefreshReg$(_c, \"FeaturePreviewSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/FeaturePreviewSection.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/sections/Footer.tsx":
/*!********************************************!*\
  !*** ./src/components/sections/Footer.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Github_Heart_Linkedin_Mail_Shield_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Heart,Linkedin,Mail,Shield,Twitter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Heart_Linkedin_Mail_Shield_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Heart,Linkedin,Mail,Shield,Twitter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Heart_Linkedin_Mail_Shield_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Heart,Linkedin,Mail,Shield,Twitter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Heart_Linkedin_Mail_Shield_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Heart,Linkedin,Mail,Shield,Twitter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Heart_Linkedin_Mail_Shield_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Heart,Linkedin,Mail,Shield,Twitter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Heart_Linkedin_Mail_Shield_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Heart,Linkedin,Mail,Shield,Twitter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst Footer = ()=>{\n    const currentYear = new Date().getFullYear();\n    const links = {\n        product: [\n            {\n                name: 'Features',\n                href: '#features'\n            },\n            {\n                name: 'Pricing',\n                href: '#pricing'\n            },\n            {\n                name: 'Demo',\n                href: '#demo'\n            },\n            {\n                name: 'API',\n                href: '#api'\n            }\n        ],\n        company: [\n            {\n                name: 'About',\n                href: '#about'\n            },\n            {\n                name: 'Blog',\n                href: '#blog'\n            },\n            {\n                name: 'Careers',\n                href: '#careers'\n            },\n            {\n                name: 'Contact',\n                href: '#contact'\n            }\n        ],\n        support: [\n            {\n                name: 'Help Center',\n                href: '#help'\n            },\n            {\n                name: 'Documentation',\n                href: '#docs'\n            },\n            {\n                name: 'Status',\n                href: '#status'\n            },\n            {\n                name: 'Community',\n                href: '#community'\n            }\n        ],\n        legal: [\n            {\n                name: 'Privacy',\n                href: '#privacy'\n            },\n            {\n                name: 'Terms',\n                href: '#terms'\n            },\n            {\n                name: 'Security',\n                href: '#security'\n            },\n            {\n                name: 'Cookies',\n                href: '#cookies'\n            }\n        ]\n    };\n    const socialLinks = [\n        {\n            icon: _barrel_optimize_names_Github_Heart_Linkedin_Mail_Shield_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            href: '#twitter',\n            label: 'Twitter'\n        },\n        {\n            icon: _barrel_optimize_names_Github_Heart_Linkedin_Mail_Shield_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            href: '#github',\n            label: 'GitHub'\n        },\n        {\n            icon: _barrel_optimize_names_Github_Heart_Linkedin_Mail_Shield_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            href: '#linkedin',\n            label: 'LinkedIn'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"relative bg-dark-950 border-t border-gray-800\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-30\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 left-1/4 w-px h-full bg-gradient-to-b from-cyan-400/20 to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 right-1/4 w-px h-full bg-gradient-to-b from-purple-400/20 to-transparent\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"py-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid lg:grid-cols-6 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 bg-gradient-to-br from-cyan-400 to-purple-400 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Heart_Linkedin_Mail_Shield_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"w-6 h-6 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n                                                            lineNumber: 66,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n                                                        lineNumber: 65,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl font-display font-bold gradient-text\",\n                                                        children: \"WarrantyAI\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n                                                        lineNumber: 68,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 mb-6 max-w-md\",\n                                                children: \"Never miss a warranty again. Smart AI assistant to track, manage, and remind you of all warranties, services, and coverage.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n                                                lineNumber: 73,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: socialLinks.map((social)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.a, {\n                                                        href: social.href,\n                                                        className: \"w-10 h-10 bg-gray-800 hover:bg-gray-700 rounded-lg flex items-center justify-center transition-colors duration-300\",\n                                                        whileHover: {\n                                                            scale: 1.1\n                                                        },\n                                                        whileTap: {\n                                                            scale: 0.95\n                                                        },\n                                                        \"aria-label\": social.label,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(social.icon, {\n                                                            className: \"w-5 h-5 text-gray-400 hover:text-white transition-colors duration-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n                                                            lineNumber: 88,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, social.label, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n                                                        lineNumber: 80,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-4 grid sm:grid-cols-2 lg:grid-cols-4 gap-8\",\n                                    children: Object.entries(links).map((param, index)=>{\n                                        let [category, categoryLinks] = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: index * 0.1\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-white font-semibold mb-4 capitalize\",\n                                                    children: category\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"space-y-3\",\n                                                    children: categoryLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: link.href,\n                                                                className: \"text-gray-400 hover:text-white transition-colors duration-300 text-sm\",\n                                                                children: link.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n                                                                lineNumber: 111,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, link.name, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n                                                            lineNumber: 110,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, category, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 17\n                                        }, undefined);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        className: \"py-8 border-t border-gray-800\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col lg:flex-row items-center justify-between space-y-4 lg:space-y-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-white mb-2\",\n                                            children: \"Stay updated with WarrantyAI\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400\",\n                                            children: \"Get the latest features and warranty management tips.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4 w-full lg:w-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 lg:w-80\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"email\",\n                                                placeholder: \"Enter your email\",\n                                                className: \"w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:border-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            variant: \"neon\",\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Heart_Linkedin_Mail_Shield_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 44\n                                            }, void 0),\n                                            children: \"Subscribe\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        whileInView: {\n                            opacity: 1\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.2\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        className: \"py-6 border-t border-gray-800 flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 text-sm text-gray-400\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"\\xa9 \",\n                                        currentYear,\n                                        \" WarrantyAI. All rights reserved.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-sm text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Made with\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Heart_Linkedin_Mail_Shield_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-4 h-4 text-red-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"for warranty peace of mind\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\Footer.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Footer;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\nvar _c;\n$RefreshReg$(_c, \"Footer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/Footer.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/sections/PricingSection.tsx":
/*!****************************************************!*\
  !*** ./src/components/sections/PricingSection.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_Crown_Infinity_Rocket_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Crown,Infinity,Rocket,Shield,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Crown_Infinity_Rocket_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Crown,Infinity,Rocket,Shield,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Crown_Infinity_Rocket_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Crown,Infinity,Rocket,Shield,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Crown_Infinity_Rocket_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Crown,Infinity,Rocket,Shield,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Crown_Infinity_Rocket_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Crown,Infinity,Rocket,Shield,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Crown_Infinity_Rocket_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Crown,Infinity,Rocket,Shield,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Crown_Infinity_Rocket_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Crown,Infinity,Rocket,Shield,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/infinity.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst plans = [\n    {\n        id: 'free',\n        name: 'Free',\n        price: 0,\n        period: 'forever',\n        description: 'Perfect for getting started with basic warranty tracking',\n        icon: _barrel_optimize_names_Check_Crown_Infinity_Rocket_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        color: 'gray',\n        popular: false,\n        features: [\n            'Up to 10 warranty items',\n            'Manual receipt upload',\n            'Basic reminders',\n            'Mobile app access',\n            'Email support'\n        ],\n        limitations: [\n            'No AI extraction',\n            'No auto-import',\n            'Limited storage'\n        ]\n    },\n    {\n        id: 'pro',\n        name: 'Pro',\n        price: 9.99,\n        period: 'month',\n        description: 'Advanced features for power users and families',\n        icon: _barrel_optimize_names_Check_Crown_Infinity_Rocket_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        color: 'cyan',\n        popular: true,\n        features: [\n            'Unlimited warranty items',\n            'AI-powered extraction',\n            'Auto-import from email',\n            'Smart reminders',\n            'Cloud storage (10GB)',\n            'Priority support',\n            'Advanced analytics',\n            'Family sharing (5 members)'\n        ],\n        limitations: []\n    },\n    {\n        id: 'business',\n        name: 'Business',\n        price: 29.99,\n        period: 'month',\n        description: 'Complete solution for businesses and teams',\n        icon: _barrel_optimize_names_Check_Crown_Infinity_Rocket_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        color: 'purple',\n        popular: false,\n        features: [\n            'Everything in Pro',\n            'Team management',\n            'Bulk import tools',\n            'API access',\n            'Custom integrations',\n            'Unlimited storage',\n            'Dedicated support',\n            'Advanced reporting',\n            'White-label options'\n        ],\n        limitations: []\n    }\n];\nconst PricingSection = ()=>{\n    _s();\n    const [hoveredPlan, setHoveredPlan] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [billingPeriod, setBillingPeriod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('monthly');\n    const getPrice = (plan)=>{\n        if (plan.price === 0) return 0;\n        return billingPeriod === 'yearly' ? plan.price * 10 : plan.price // 2 months free on yearly\n        ;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-24 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-b from-dark-950 via-dark-900 to-dark-950\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/4 left-1/4 w-64 h-64 bg-cyan-400/10 rounded-full blur-3xl animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-1/4 right-1/4 w-80 h-80 bg-purple-400/10 rounded-full blur-3xl animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.h2, {\n                                className: \"text-4xl lg:text-6xl font-display font-bold mb-6\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"gradient-text\",\n                                    children: \"Simple Pricing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.p, {\n                                className: \"text-xl text-gray-300 max-w-3xl mx-auto mb-8\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.2\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: \"Choose the perfect plan for your warranty tracking needs. Start free, upgrade anytime.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.4\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"flex items-center justify-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm font-medium transition-colors duration-300\", billingPeriod === 'monthly' ? \"text-white\" : \"text-gray-400\"),\n                                        children: \"Monthly\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setBillingPeriod(billingPeriod === 'monthly' ? 'yearly' : 'monthly'),\n                                        className: \"relative w-14 h-7 bg-gray-700 rounded-full transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-cyan-400\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                            className: \"absolute top-1 w-5 h-5 bg-white rounded-full shadow-lg\",\n                                            animate: {\n                                                x: billingPeriod === 'yearly' ? 32 : 4\n                                            },\n                                            transition: {\n                                                type: \"spring\",\n                                                stiffness: 500,\n                                                damping: 30\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm font-medium transition-colors duration-300\", billingPeriod === 'yearly' ? \"text-white\" : \"text-gray-400\"),\n                                                children: \"Yearly\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-green-400/20 text-green-400 text-xs px-2 py-1 rounded-full\",\n                                                children: \"Save 20%\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-3 gap-8 max-w-5xl mx-auto\",\n                        children: plans.map((plan, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: index * 0.2\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"relative\",\n                                onMouseEnter: ()=>setHoveredPlan(plan.id),\n                                onMouseLeave: ()=>setHoveredPlan(null),\n                                children: [\n                                    plan.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-4 left-1/2 transform -translate-x-1/2 z-20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-r from-cyan-400 to-purple-400 text-dark-900 px-4 py-1 rounded-full text-sm font-medium flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Crown_Infinity_Rocket_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Most Popular\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"glass-card p-8 h-full relative overflow-hidden transition-all duration-500\", plan.popular && \"border-2 border-cyan-400/50\", hoveredPlan === plan.id && \"scale-105 shadow-2xl\"),\n                                        animate: {\n                                            borderRadius: hoveredPlan === plan.id ? \"24px\" : \"20px\"\n                                        },\n                                        transition: {\n                                            duration: 0.3\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.AnimatePresence, {\n                                                children: hoveredPlan === plan.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0\n                                                    },\n                                                    animate: {\n                                                        opacity: 1\n                                                    },\n                                                    exit: {\n                                                        opacity: 0\n                                                    },\n                                                    className: \"absolute inset-0 bg-\".concat(plan.color === 'cyan' ? 'cyan' : plan.color === 'purple' ? 'purple' : 'gray', \"-400/5 rounded-2xl\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative z-10\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center mb-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"w-16 h-16 mx-auto rounded-full flex items-center justify-center mb-4 transition-all duration-300\", plan.color === 'cyan' ? \"bg-cyan-400/20 border-2 border-cyan-400\" : plan.color === 'purple' ? \"bg-purple-400/20 border-2 border-purple-400\" : \"bg-gray-400/20 border-2 border-gray-400\"),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(plan.icon, {\n                                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"w-8 h-8\", plan.color === 'cyan' ? \"text-cyan-400\" : plan.color === 'purple' ? \"text-purple-400\" : \"text-gray-400\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                                                    lineNumber: 217,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                                                lineNumber: 211,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-2xl font-bold text-white mb-2\",\n                                                                children: plan.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                                                lineNumber: 225,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-400 text-sm\",\n                                                                children: plan.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                                                lineNumber: 226,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center mb-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-baseline justify-center space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-4xl font-bold text-white\",\n                                                                        children: [\n                                                                            \"$\",\n                                                                            getPrice(plan)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                                                        lineNumber: 232,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    plan.price > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-400\",\n                                                                        children: [\n                                                                            \"/\",\n                                                                            billingPeriod === 'yearly' ? 'year' : plan.period\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                                                        lineNumber: 236,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                                                lineNumber: 231,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            billingPeriod === 'yearly' && plan.price > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-green-400 mt-1\",\n                                                                children: [\n                                                                    \"Save $\",\n                                                                    (plan.price * 2).toFixed(2),\n                                                                    \" per year\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                                                lineNumber: 243,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4 mb-8\",\n                                                        children: plan.features.map((feature, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                                initial: {\n                                                                    opacity: 0,\n                                                                    x: -10\n                                                                },\n                                                                whileInView: {\n                                                                    opacity: 1,\n                                                                    x: 0\n                                                                },\n                                                                transition: {\n                                                                    delay: idx * 0.1\n                                                                },\n                                                                viewport: {\n                                                                    once: true\n                                                                },\n                                                                className: \"flex items-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Crown_Infinity_Rocket_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"w-5 h-5 flex-shrink-0\", plan.color === 'cyan' ? \"text-cyan-400\" : plan.color === 'purple' ? \"text-purple-400\" : \"text-gray-400\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                                                        lineNumber: 260,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-300 text-sm\",\n                                                                        children: feature\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                                                        lineNumber: 266,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, feature, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 23\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        variant: plan.popular ? \"neon\" : \"glass\",\n                                                        size: \"lg\",\n                                                        className: \"w-full\",\n                                                        icon: plan.id === 'free' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Crown_Infinity_Rocket_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                                            lineNumber: 276,\n                                                            columnNumber: 48\n                                                        }, void 0) : plan.id === 'pro' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Crown_Infinity_Rocket_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                                            lineNumber: 276,\n                                                            columnNumber: 81\n                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Crown_Infinity_Rocket_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                                            lineNumber: 276,\n                                                            columnNumber: 91\n                                                        }, void 0),\n                                                        children: plan.id === 'free' ? 'Start Free' : 'Get Started'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    plan.limitations.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-6 pt-6 border-t border-gray-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 mb-2\",\n                                                                children: \"Limitations:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                                                lineNumber: 284,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            plan.limitations.map((limitation, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 text-xs text-gray-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-1 h-1 bg-gray-500 rounded-full\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                                                            lineNumber: 287,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: limitation\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                                                            lineNumber: 288,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, limitation, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                                                    lineNumber: 286,\n                                                                    columnNumber: 25\n                                                                }, undefined))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, plan.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.6\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        className: \"text-center mt-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center space-x-8 text-gray-400\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Crown_Infinity_Rocket_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-5 h-5 text-green-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm\",\n                                            children: \"30-day money back\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Crown_Infinity_Rocket_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-5 h-5 text-cyan-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm\",\n                                            children: \"Cancel anytime\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Crown_Infinity_Rocket_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-5 h-5 text-yellow-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm\",\n                                            children: \"4.9/5 rating\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\pjs\\\\i5-d3-warrantyai\\\\src\\\\components\\\\sections\\\\PricingSection.tsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PricingSection, \"oESfzm0H0gcL+5+u6OjSWGp1pHI=\");\n_c = PricingSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PricingSection);\nvar _c;\n$RefreshReg$(_c, \"PricingSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/PricingSection.tsx\n"));

/***/ })

});